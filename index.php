<?php

//check token
$token = filter_input(INPUT_GET, 'token');
$checkToken = md5('flussi' . date('dmY')) === $token ? true : false;

//if ($checkToken) {
    if (!function_exists('mt_autoload')) {
        function mt_autoload($class) {
        // If arrives here it try to search with namespace
            $path = '/var/www-source/mt/1.2/classes/' . str_replace('\\', '/', $class) . '.php';

            if (file_exists($path)) {
                include_once $path;
            }
        }

        spl_autoload_register('mt_autoload');
    }

    spl_autoload_register(function ($class_name) {
        include 'classes/'. $class_name . '.php';
    });
//} else {
//    exit ('Token errato');
//}