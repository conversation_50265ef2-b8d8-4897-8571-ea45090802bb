<?php

class Siis
{

    public $flusso;
    public $database;
    private $_errors;
    protected $db;

    function __construct($flusso, $database)
    {
        $this->flusso = $flusso;
        $this->database = $database;

        $db = new MT\Mastercom\Db();
        $dbcorrente = $db->getDatabaseParameters();

        if ($dbcorrente['name'] != $this->database)
        {
            $dbcorrente['name'] = $this->database;
        }

        $db->setDatabaseParameters((array) $dbcorrente);
        $this->db = $db;
    }

    public function getLastErrors()
    {
        return $this->_errors;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     * @return array
     */
    public function AnnoScolastico()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return explode('/', trim($this->db->query($query)));
    }

    public function Scuola()
    {
        $query = "SELECT DISTINCT
                        scuole.id_scuola,
                        scuole.descrizione,
                        codice_meccanografico,
                        codice_meccanografico_secondario,
                        scuole.codice_prenotazione,
                        sedi.id_sede,
                        sedi.descrizione as sedi_descrizione,
                        codice_meccanografico||'_'||codice_meccanografico_secondario||'.json' AS codice_file
                    FROM scuole, sedi, indirizzi
                    WHERE scuole.id_scuola = sedi.id_scuola::int
                        AND sedi.id_sede = indirizzi.id_sede::bigint
                        AND indirizzi.flag_canc = 0
                        AND scuole.flag_canc = 0
                        AND sedi.flag_canc = 0";

        return $this->db->query($query);
    }

    public function Sede()
    {
        return $select;
    }

    public function Alunno()
    {
        return $select;
    }

    public function Curriculum()
    {
        return $select;
    }

    public function getFiles($meccanografico, $flusso)
    {

    }

    public function SFA($meccanografico, $operazione, $as)
    {
        $sfaClass = new SiisScrutiniFinaliAnaltici($meccanografico, $as);

        switch ($operazione)
        {
            case 'verify':
                return $sfaClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $sfaClass->generate($meccanografico);
                break;
            case 'merge':
                return $sfaClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function CS($meccanografico, $operazione, $as)
    {
        $csClass = new SiisCreditiScolastici($meccanografico, $as);

        switch ($operazione)
        {
            case 'verify':
                return $csClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $csClass->generate($meccanografico);
                break;
            case 'merge':
                return $csClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function EF($meccanografico, $operazione, $as)
    {
        $efClass = new SiisEsitiFinali($meccanografico, $as);

        switch ($operazione)
        {
            case 'verify':
                return $efClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $efClass->generate($meccanografico);
                break;
            case 'merge':
                return $efClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function EP($meccanografico, $operazione, $as)
    {
        $epClass = new SiisEsitiPrimaria($meccanografico, $as);

        switch ($operazione)
        {
            case 'verify':
                return $epClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $epClass->generate($meccanografico);
                break;
            case 'merge':
                return $epClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }


    public function ESES($meccanografico, $operazione, $as)
    {
        $esesClass = new SiisEsitiEsamiStato($meccanografico, $as);

        switch ($operazione)
        {
            case 'verify':
                return $esesClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $esesClass->generate($meccanografico);
                break;
            case 'merge':
                return $esesClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function AN($meccanografico, $operazione)
    {
        $anClass = new SiisAnagrafeNazionale($meccanografico);

        switch ($operazione)
        {
            case 'verify':
                return $anClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $anClass->generate($meccanografico);
                break;
            case 'merge':
                return $anClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function RA($meccanografico, $operazione, $as)
    {
        $raClass = new SiisAssenze($meccanografico, $as);

        switch ($operazione)
        {
            case 'verify':
                return $raClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $raClass->generate($meccanografico);
                break;
            case 'merge':
                return $raClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function FI($meccanografico, $operazione)
    {
        $fiClass = new SiisFrequenzaInfanzia($meccanografico);

        switch ($operazione)
        {
            case 'verify':
                return $fiClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $fiClass->generate($meccanografico);
                break;
            case 'merge':
                return $fiClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function AG($meccanografico, $operazione)
    {
        $agClass = new SiisAssociazioneGenitoreAlunno($meccanografico);

        switch ($operazione)
        {
            case 'verify':
                return $agClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $agClass->generate($meccanografico);
                break;
            case 'merge':
                return $agClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function E7($meccanografico, $operazione)
    {
        $e7Class = new SiisAssociazioneDocenteClasse($meccanografico);

        switch ($operazione)
        {
            case 'verify':
                return $e7Class->verify($meccanografico);
                break;
            case 'elaborate':
                return $e7Class->generate($meccanografico);
                break;
            case 'merge':
                return $e7Class->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }

    public function CO($meccanografico, $operazione)
    {
        $coClass = new SiisConsiglioOrientativo($meccanografico);

        switch ($operazione)
        {
            case 'verify':
                return $coClass->verify($meccanografico);
                break;
            case 'elaborate':
                return $coClass->generate($meccanografico);
                break;
            case 'merge':
                return $coClass->mergeFile($meccanografico);
                break;
            default:
                break;
        }
    }
}
