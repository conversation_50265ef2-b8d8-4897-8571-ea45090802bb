<?php

require_once '/var/www/mastertek-api/tcpdf/6.0.090/tcpdf.php';

class PDFClass extends TCPDF {

    public $widths;
    public $aligns;
    public $angle = 0;
    public $legends;
    public $wLegend;
    public $sum;
    public $NbVal;

    protected $print_header = false;
    protected $print_footer = false;

    /**
     * Funzione necessaria per rimappare il font con quello corretto per TCPDF,
     * altrimenti le nuove versioni dei viewer di Chrome e FF non visualizzano
     * il testo
     *
     * @param string       $family
     * @param string       $style
     * @param float        $size
     * @param string|mixed $fontfile
     * @param string       $subset
     * @param boolean      $out
     * @return type
     */
    public function SetFont($family, $style = '', $size = null, $fontfile = '', $subset = 'default', $out = true) {
        if (strtolower($family) === 'arial') {
            $family = 'dejavusans';
        }

        return parent::SetFont($family, $style, $size, $fontfile, $subset, $out);
    }

    public function FancyTable($data, $header_0_0, $totale = 0, $multiple = 0) {
        //{{{ <editor-fold defaultstate="collapsed" desc=" crea una tabella in un pdf con l'intestazione colorata">
        //Colors, line width and bold font
        $this->SetFillColor(255, 0, 0);
        $this->SetTextColor(255);
        $this->SetDrawColor(128, 0, 0);
        $this->SetLineWidth(.3);
        $this->SetFont('', 'B');

        $w2 = 25;
        $w = 7.5;
        $h = 6;
        $this->Cell($w2, $h + 1, $header_0_0, 1, 0, 'C', 1);

        for ($i = 1; $i < 32; $i++) {
            $this->Cell($w, $h + 1, $i, 1, 0, 'C', 1);
        }

        //controllo se è stato richiesto il totale alla fine del mese e ne inserisco la label
        if ($totale == 1) {
            $this->Cell($w + 4, $h + 1, "TOT", 1, 0, 'C', 1);
        }

        $this->Ln();
        //Color and font restoration
        $this->SetFillColor(224, 235, 255);
        $this->SetTextColor(0);
        $this->SetFont('');

        //Data
        $fill = 0;

        for ($cont = 0; $cont < 12; $cont++) {
            for ($cont2 = 0; $cont2 < 32; $cont2++) {
                if ($cont2 == 0) {
                    $this->SetTextColor(0);
                    $this->Cell($w2, $h, $data[$cont][$cont2][0], 'LRTB', 0, 'C', $fill);
                } else {
                    //se è abilitato la molteplicità dei valori per cella controllo anche gli altri 2 valori ammessi e se ci sono li inserisco
                    if ($multiple == 1 && $data[$cont][$cont2][10] != "" && $data[$cont][$cont2][20] != "") {
                        $this->SetFont('dejavusans', '', 10);
                        $this->SetTextColor($data[$cont][$cont2][1], $data[$cont][$cont2][2], $data[$cont][$cont2][3]);
                        $this->Cell($w / 3, $h, $data[$cont][$cont2][0], 'LTB', 0, 'C', $fill);
                        $this->SetTextColor($data[$cont][$cont2][11], $data[$cont][$cont2][12], $data[$cont][$cont2][13]);
                        $this->Cell($w / 3, $h, $data[$cont][$cont2][10], 'TB', 0, 'C', $fill);
                        $this->SetTextColor($data[$cont][$cont2][21], $data[$cont][$cont2][22], $data[$cont][$cont2][23]);
                        $this->Cell($w / 3, $h, $data[$cont][$cont2][20], 'RTB', 0, 'C', $fill);
                    } else {
                        //questo controllo serve per distribuire meglio le assenzse se ce ne sono due invece che tre
                        if ($multiple == 1 && $data[$cont][$cont2][10] != "") {
                            $this->SetFont('dejavusans', '', 10);
                            $this->SetTextColor($data[$cont][$cont2][1], $data[$cont][$cont2][2], $data[$cont][$cont2][3]);
                            $this->Cell($w / 3, $h, $data[$cont][$cont2][0], 'LTB', 0, 'C', $fill);

                            $this->Cell($w / 3, $h, "", 'TB', 0, 'C', $fill);

                            $this->SetTextColor($data[$cont][$cont2][11], $data[$cont][$cont2][12], $data[$cont][$cont2][13]);
                            $this->Cell($w / 3, $h, $data[$cont][$cont2][10], 'RTB', 0, 'C', $fill);
                        } else {
                            $this->SetFont('dejavusans', '', 12);
                            $this->SetTextColor($data[$cont][$cont2][1], $data[$cont][$cont2][2], $data[$cont][$cont2][3]);
                            $this->Cell($w, $h, $data[$cont][$cont2][0], 'LRTB', 0, 'C', $fill);
                        }
                    }
                    $this->SetFont('');
                }
            }

            //controllo se è stato richiesto il totale alla fine del mese e ne inserisco il valore
            if ($totale == 1) {
                $this->SetTextColor(0);
                $this->Cell($w + 4, $h, $data[$cont][40][0], 1, 0, 'C', $fill);
            }

            $this->Ln();
            $fill = !$fill;
        }
        //}}} </editor-fold>
    }

    //Cell with horizontal scaling if text is too wide
    public function CellFit($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = 0, $link = '', $scale = 0, $force = 1) {
        $this->Cell($w, $h, $txt, $border, $ln, $align, $fill, $link, $scale);
    }

    //Cell with horizontal scaling only if necessary
    public function CellFitScale($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = 0, $link = '') {
        $this->CellFit($w, $h, $txt, $border, $ln, $align, $fill, $link, 1, 0);
    }

    public function MultiCellNbLines($w, $txt) {
        //{{{ <editor-fold defaultstate="collapsed" desc="Computes the number of lines a MultiCell of width w will take">
        $this->startTransaction();
        $numero_righe = $this->MultiCell($w, 5, $txt);
        $this->rollbackTransaction(true);

        return $numero_righe;
        //}}} </editor-fold>
    }

    public function Code39($xpos, $ypos, $code, $baseline = 0.5, $height = 5, $testo = "SI") {
        //{{{ <editor-fold defaultstate="collapsed">
        if ($testo == "SI") {
            $style['text'] = true;
        } else {
            $style['text'] = false;
        }

        $this->write1DBarcode($code, 'C39', $xpos, $ypos, '', '', $baseline, $style, '');
        //}}} </editor-fold>
    }

    public function PieChart($w, $h, $data, $format, $colors = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $this->SetFont('Courier', '', 10);
        $this->SetLegends($data, $format);

        $XPage = $this->GetX();
        $YPage = $this->GetY();
        $margin = 2;
        $hLegend = 5;

        $radius = min($w - $margin * 4 - $hLegend - $this->wLegend, $h - $margin * 2);
        $radius = floor($radius / 2);

        $XDiag = $XPage + $margin + $radius;
        $YDiag = $YPage + $margin + $radius;

        if ($colors == null) {
            for ($i = 0; $i < $this->NbVal; $i++) {
                $gray = $i * intval(255 / $this->NbVal);
                $colors[$i] = [$gray, $gray, $gray];
            }
        }

        //Sectors
        $this->SetLineWidth(0.2);
        $angleStart = 0;
        $angleEnd = 0;
        $i = 0;

        foreach ($data as $val) {
            if ($this->sum > 0) {
                $divisione = floor(($val * 360) / doubleval($this->sum));
            } else {
                $divisione = 0;
            }
            $angle = $divisione;
            if ($angle != 0) {
                $angleEnd = $angleStart + $angle;
                $this->SetFillColor($colors[$i][0], $colors[$i][1], $colors[$i][2]);
                $this->Sector($XDiag, $YDiag, $radius, $angleStart, $angleEnd);
                $angleStart += $angle;
            }
            $i++;
        }

        if ($angleEnd != 360) {
            $this->Sector($XDiag, $YDiag, $radius, $angleStart - $angle, 360);
        }

        //Legends
        $this->SetFont('Courier', '', 10);
        $x1 = $XPage + 2 * $radius + 4 * $margin;
        $x2 = $x1 + $hLegend + $margin;
        $y1 = $YDiag - $radius + (2 * $radius - $this->NbVal * ($hLegend + $margin)) / 2;

        for ($i = 0; $i < $this->NbVal; $i++) {
            $this->SetFillColor($colors[$i][0], $colors[$i][1], $colors[$i][2]);
            $this->Rect($x1, $y1, $hLegend, $hLegend, 'DF');
            $this->SetXY($x2, $y1);
            $this->Cell(0, $hLegend, $this->legends[$i]);
            $y1+=$hLegend + $margin;
        }
        //}}} </editor-fold>
    }

    public function SetLegends($data, $format) {
        //{{{ <editor-fold defaultstate="collapsed">
        $this->legends = [];
        $this->wLegend = 0;
        $this->sum = array_sum($data);
        $this->NbVal = count($data);

        foreach ($data as $l => $val) {
            if ($this->sum > 0) {
                $divisione = $val / $this->sum * 100;
            } else {
                $divisione = 0;
            }

            $p = sprintf('%.2f', $divisione) . '%';
            $legend = str_replace(['%l', '%v', '%p'], [$l, $val, $p], $format);
            $this->legends[] = $legend;
            $this->wLegend = max($this->GetStringWidth($legend), $this->wLegend);
        }
        //}}} </editor-fold>
    }

    public function Sector($xc, $yc, $r, $a, $b, $style = 'FD', $cw = true, $o = 90) {
        //{{{ <editor-fold defaultstate="collapsed">
        if ($cw) {
            $d = $b;
            $b = $o - $a;
            $a = $o - $d;
        } else {
            $b += $o;
            $a += $o;
        }

        $a = ($a % 360) + 360;
        $b = ($b % 360) + 360;

        if ($a > $b) {
            $b +=360;
        }

        $b = $b / 360 * 2 * M_PI;
        $a = $a / 360 * 2 * M_PI;
        $d = $b - $a;

        if ($d == 0) {
            $d = 2 * M_PI;
        }

        $k = $this->k;
        $hp = $this->h;

        if ($style == 'F') {
            $op = 'f';
        } elseif ($style == 'FD' or $style == 'DF') {
            $op = 'b';
        } else {
            $op = 's';
        }

        if (sin($d / 2)) {
            $MyArc = 4 / 3 * (1 - cos($d / 2)) / sin($d / 2) * $r;
        }

        //first put the center
        $this->_out(sprintf('%.2f %.2f m', ($xc) * $k, ($hp - $yc) * $k));
        //put the first point
        $this->_out(sprintf('%.2f %.2f l', ($xc + $r * cos($a)) * $k, (($hp - ($yc - $r * sin($a))) * $k)));

        //draw the arc
        if ($d < M_PI / 2) {
            $this->_Arc($xc + $r * cos($a) + $MyArc * cos(M_PI / 2 + $a), $yc - $r * sin($a) - $MyArc * sin(M_PI / 2 + $a), $xc + $r * cos($b) + $MyArc * cos($b - M_PI / 2), $yc - $r * sin($b) - $MyArc * sin($b - M_PI / 2), $xc + $r * cos($b), $yc - $r * sin($b));
        } else {
            $b = $a + $d / 4;
            $MyArc = 4 / 3 * (1 - cos($d / 8)) / sin($d / 8) * $r;
            $this->_Arc($xc + $r * cos($a) + $MyArc * cos(M_PI / 2 + $a), $yc - $r * sin($a) - $MyArc * sin(M_PI / 2 + $a), $xc + $r * cos($b) + $MyArc * cos($b - M_PI / 2), $yc - $r * sin($b) - $MyArc * sin($b - M_PI / 2), $xc + $r * cos($b), $yc - $r * sin($b)
            );
            $a = $b;
            $b = $a + $d / 4;
            $this->_Arc($xc + $r * cos($a) + $MyArc * cos(M_PI / 2 + $a), $yc - $r * sin($a) - $MyArc * sin(M_PI / 2 + $a), $xc + $r * cos($b) + $MyArc * cos($b - M_PI / 2), $yc - $r * sin($b) - $MyArc * sin($b - M_PI / 2), $xc + $r * cos($b), $yc - $r * sin($b)
            );
            $a = $b;
            $b = $a + $d / 4;
            $this->_Arc($xc + $r * cos($a) + $MyArc * cos(M_PI / 2 + $a), $yc - $r * sin($a) - $MyArc * sin(M_PI / 2 + $a), $xc + $r * cos($b) + $MyArc * cos($b - M_PI / 2), $yc - $r * sin($b) - $MyArc * sin($b - M_PI / 2), $xc + $r * cos($b), $yc - $r * sin($b)
            );
            $a = $b;
            $b = $a + $d / 4;
            $this->_Arc($xc + $r * cos($a) + $MyArc * cos(M_PI / 2 + $a), $yc - $r * sin($a) - $MyArc * sin(M_PI / 2 + $a), $xc + $r * cos($b) + $MyArc * cos($b - M_PI / 2), $yc - $r * sin($b) - $MyArc * sin($b - M_PI / 2), $xc + $r * cos($b), $yc - $r * sin($b)
            );
        }

        //terminate drawing
        $this->_out($op);
        //}}} </editor-fold>
    }

    public function _Arc($x1, $y1, $x2, $y2, $x3, $y3) {
        $h = $this->h;
        $this->_out(sprintf('%.2f %.2f %.2f %.2f %.2f %.2f c', $x1 * $this->k, ($h - $y1) * $this->k, $x2 * $this->k, ($h - $y2) * $this->k, $x3 * $this->k, ($h - $y3) * $this->k));
    }

}
