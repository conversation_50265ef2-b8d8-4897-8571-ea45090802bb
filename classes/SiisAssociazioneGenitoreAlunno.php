<?php


class SiisAssociazioneGenitoreAlunno extends Siis {

    protected $db;
    protected $pdf;

    function __construct() {
        $db = new MT\Mastercom\Db();
        $this->db = $db;

        $pdf = new PDFClass();
        $this->pdf = $pdf;
    }

    function decode($field) {
        if (!is_array($field) && strlen($field) > 0) {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        } else {
            return $field;
        }
    }

    public function normalize_string($string) {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico() {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    public function getStudenteDaCodiceSidi($codiceSidi) {
        $query = "SELECT cognome,nome FROM studenti WHERE codice_alunno_ministeriale = '" . $codiceSidi ."'";
        return $this->db->query($query);
    }
    /**
     * Crea lo zip per ogni file .txt generato
     *
     * $filename riporta il prefisso dei tre file dell'anagrafe nazionale
     *
     * @return int
     */
    public function createZip($fileName, $codMecc, $timestamp = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;
        exec("rm /var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/GENALU_{$codMecc}*{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/{$codMecc}*.TXT");
        file_put_contents("/tmp/glob",print_r($glob,true));

        foreach ($glob as $file) {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/GENALU_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE) {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/*.TXT");
        //}}} </editor-fold>
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $cod
     */
//    public function mergeFile($cod) {
//        //{{{ <editor-fold defaultstate="collapsed">
//        $annoScolatico = $this->getAnnoScolastico();
//        $timestamp = date('YmdHi');
//        $timestamp2 = date('Ymd');
//        $codMecc = explode('-', $cod)[1];
//        $path_files = "/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/";
//
//        // creo i file finali
//        $codici_scuola = $this->getCodiciScuola($cod);
//        $prefisso_file = $codMecc .
//                        $codici_scuola['codice_prenotazione'] .
//                        $this->formatId($codici_scuola['id_scuola']) .
//                        explode("/", $annoScolatico['valore'])[0] .
//                        "00FI" .
//                        "MAST" .
//                        "20160617" .
//                        $timestamp;
//
//        $glob = glob($path_files . "AVVIOSCUOLAINFANZIA_{$codMecc}_{$timestamp2}*.zip");
//
//        // recupero i due file: quello creato in locale e quello creato sull'altro server
//        foreach ($glob as $key => $file) {
//            $basename = basename($file);
//            $zip = new ZipArchive;
//            $nomeZip = $path_files . $basename;
//
//            if ($zip->open($nomeZip) == TRUE) {
//                exec("mkdir {$path_files}tmp/{$key}/");
//
//                $zip->extractTo("{$path_files}tmp/{$key}/");
//                $zip->close();
//
//                $glob_zip = glob("{$path_files}tmp/{$key}/*");
//
//                foreach ($glob_zip as $file_singolo) {
//                    if (strpos($file_singolo, "001TB_USR_AS.TXT") !== false) {
//                        // anno scolastico (sovrascrivo in quanto la scuola è una sola)
//                        $file_anno_scolastico = file_get_contents($file_singolo);
//                        file_put_contents("{$path_files}{$prefisso_file}001TB_USR_AS.TXT", print_r($file_anno_scolastico, true));
//                    }
//
//                    if (strpos($file_singolo, "004TB_USR_USER.TXT") !== false) {
//                        // scuola (sovrascrivo in quanto la scuola è una sola)
//                        $file_scuola = file_get_contents($file_singolo);
//                        file_put_contents("{$path_files}{$prefisso_file}004TB_USR_USER.TXT", print_r($file_scuola, true));
//                    }
//
//                    if (strpos($file_singolo, "005TB_USR_SEDI.TXT") !== false) {
//                        // sedi
//                        $file_sedi = file_get_contents($file_singolo);
//                        file_put_contents("{$path_files}{$prefisso_file}005TB_USR_SEDI.TXT", print_r($file_sedi, true), FILE_APPEND);
//                    }
//
//                    if (strpos($file_singolo, "006TB_ALU_SEZ_ANAG.TXT") !== false) {
//                        // anagrafica sezione
//                        $file_sezioni = file_get_contents($file_singolo);
//                        file_put_contents("{$path_files}{$prefisso_file}006TB_ALU_SEZ_ANAG.TXT", print_r($file_sezioni, true), FILE_APPEND);
//                    }
//
//                    if (strpos($file_singolo, "007TB_ALU_ALUNNI.TXT") !== false) {
//                        // studenti
//                        $file_studenti = file_get_contents($file_singolo);
//                        file_put_contents("{$path_files}{$prefisso_file}007TB_ALU_ALUNNI.TXT", print_r($file_studenti, true), FILE_APPEND);
//                    }
//
//                    if (strpos($file_singolo, "008TB_ALU_SIT.TXT") !== false) {
//                        // curriculum
//                        $file_curriculum = file_get_contents($file_singolo);
//                        file_put_contents("{$path_files}{$prefisso_file}008TB_ALU_SIT.TXT", print_r($file_curriculum, true), FILE_APPEND);
//                    }
//                }
//
//                exec("rm {$path_files}tmp/{$key}/*");
//            } else {
//                echo 'Merge files failed';
//            }
//        }
//
//        // creo lo zip finale
//        $this->createZip($prefisso_file, $codMecc, $timestamp);
//
//        // creo il .dat
//        file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/##" . $prefisso_file . ".dat", "");
//
//        $fileZip = glob($path_files . 'AVVIOSCUOLAINFANZIA_' . $codMecc . '_' . $timestamp . '.zip');
//        $zip = new ZipArchive;
//
//        foreach ($fileZip as $zipFile) {
//            if ($zip->open($zipFile) === TRUE) {
//                $zip->addFile('/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
//                        or die ("ERROR: Could not add file: {$prefisso_file}");
//                $zip->close();
//            }
//        }
//        //}}} </editor-fold>
//    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        // Scuola
        $scuola = $this->getScuola($meccanografico, 'verify');
        if (!empty($scuola['errors'])) {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Scuola',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
        }

        // Sede
        $sede = $this->getSede($meccanografico, 'verify');
        if (!empty($sede['errors']['codice_sidi'])) {
            foreach ($sede['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Sede',
                    'valore' => $value
                ];
            }
        }
        // Studenti
        $studenti = $this->getStudenti($meccanografico, 'verify');
        if (!empty($studenti['errors']['codice_sidi'])) {
            foreach ($studenti['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Studenti',
                    'valore' => $value
                ];
            }
        }


        if (!empty($result[$meccanografico]['errors_stop'])) {
            return $result[$meccanografico]['errors_stop'];
        } else {
            return [];
        }
        //}}} </editor-fold>
    }

    /**
     * Elabora i file
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function generate($meccanografico) {
        return $this->associazione_genitore_alunno($meccanografico);
    }

       /**
     * Restituisce i dati dell'anno scolastico
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getDatiAnnoScolastico($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);

        // inizo lezioni
        $query_inizio = "SELECT valore FROM parametri WHERE nome = 'DATA_INIZIO_ANNO_SCOLASTICO' AND flag_canc = 0";
        $inizio =  explode("/", $this->db->query($query_inizio)['valore']);

        // fine lezioni
        $query_fine = "SELECT valore FROM parametri WHERE nome = 'DATA_FINE_ANNO_SCOLASTICO' AND flag_canc = 0";
        $fine =  explode("/", $this->db->query($query_fine)['valore']);

        $datiAS[$codici_scuola['codice_prenotazione']] = [
            "fine"              => $anno_scolastico[1] . "-" . $fine[1] . "-" . $fine[0],
            "inizio"            => $anno_scolastico[0] . "-" . $inizio[1] . "-" . $inizio[0],
            "anno_scolastico"   => $anno_scolastico[0],
            "ordine_scuola"     => $codici_scuola['codice_prenotazione'] . "AA",
        ];

        return $datiAS;
        //}}} </editor-fold>
    }

   /**
     * Restituisce i dati della scuola in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiScuola = $errors = [];

        $mecc = explode('-', $meccanografico);
        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $query = "SELECT DISTINCT
                        sc.id_scuola,
                        sc.codice_prenotazione,
                        sc.codice_meccanografico,
                        sc.codice_meccanografico_secondario,
                        sedi.id_comune
                    FROM scuole sc,
                        sedi,
                        indirizzi,
                        indirizzi_ministeriali
                    WHERE sc.id_scuola = sedi.id_scuola::int
                        AND sedi.id_sede = indirizzi.id_sede::bigint
                        AND indirizzi.id_codice_ministeriale = indirizzi_ministeriali.id_indirizzo
                        AND indirizzi.flag_canc = 0
                        AND sc.flag_canc = 0
                        AND sedi.flag_canc = 0
                        AND indirizzi_ministeriali.flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                    LIMIT 1";

        $arrScuola =  $this->db->query($query);

        // verifica dati
        if (!empty($arrScuola)) {
            if ($arrScuola['codice_prenotazione'] == '') {
                $errors['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
            if ($arrScuola['id_comune'] == '') {
                $errors['id_comune'][] = "Errore: manca il codice del comune per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
        } else {
            $errors['codice_prenotazione'][] = "Dati Scuola mancanti per la scuola {$mecc[0]} - {$mecc[1]}";
        }

        $datiScuola[$arrScuola['id_scuola']] = [
            'codice_meccanografico_debole_scuola'	=>	$arrScuola['codice_meccanografico_secondario'],
            'codice_comune'							=>	$arrScuola['id_comune'],
            'identificativo_scuola' 				=>	$codici_scuola['codice_prenotazione'] . $this->formatId($arrScuola['id_scuola']),
        ];

        if ($check == 'verify' && !empty($errors)) {
            // aggiungo gli eventuali errori
            $datiScuola['errors'] = $errors;
        }

        return $datiScuola;
        //}}} </editor-fold>
    }

    /**
     * Restituisce i dati della scuola in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getSede($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiSede = $errors = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);

        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);
        $datiScuola = $this->getScuola($meccanografico);
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                        cap,
                        id_comune,
                        descrizione,
                        fax,
                        id_sede,
                        indirizzo,
                        id_scuola,
                        telefono,
                        data_inizio_validita,
                        data_fine_validita
                    FROM sedi
                    WHERE sedi.flag_canc = 0
                        AND id_scuola = '{$codici_scuola['id_scuola']}'";

        $arrSede = $this->db->query($query);

        $data_inizio_validita = $arrSede['data_inizio_validita'] ? explode('/', $arrSede['data_inizio_validita']) : ['01', '09', $anno_scolastico[0]];
        $data_fine_validita = $arrSede['data_fine_validita'] ? explode('/', $arrSede['data_fine_validita']) : [];

        // verifica dati
        if (!empty($arrSede)) {
            if ($arrSede['id_comune'] == '') {
                $errors['id_comune'][] = "Errore: manca il codice del comune per la sede della scuola {$arrSede['descrizione']}";
            }
        } else {
            $errors['codice_prenotazione'][] = "Dati Scuola mancanti per la scuola {$mecc[0]} - {$mecc[1]}";
        }

        $datiSede[$arrSede["id_scuola"]] = [
            'cap'                   => $arrSede['cap'],
            'codice_comune'         => $arrSede['id_comune'],
            'denominazione_sede'    => $this->decode($arrSede['descrizione']),
            'fax'                   => $arrSede['fax'],
            'identificativo_sede'   => $codici_scuola['codice_prenotazione'] . $this->formatId($arrSede['id_sede']),
            'indirizzo_sede'        => $arrSede['indirizzo'],
            'identificativo_scuola' => $codici_scuola['codice_prenotazione'] . $this->formatId($arrSede['id_scuola']),
            'telefono'              => $arrSede['telefono'],
            'data_inizio_validita'  => $data_inizio_validita[2] . '-' . $data_inizio_validita[1] . '-' . $data_inizio_validita[0],
            'data_fine_validita'    => !empty($data_fine_validita) ? $data_fine_validita[2] . '-' . $data_fine_validita[1] . '-' . $data_fine_validita[0] : ''
        ];

        if ($check == 'verify') {
            // aggiungo gli eventuali errori
            $datiSede['errors'] = $errors;
        }

        return $datiSede;
        //}}} </editor-fold>
    }



    /**
     * Restituisce i dati degli alunni in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $errors = [];
        $datiStudenti = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $mecc = explode('-', $meccanografico);

        $query = "SELECT
                        studenti_completi.id_studente,
                        studenti_completi.cognome,
                        studenti_completi.nome,
                        studenti_completi.codice_fiscale,
                        codice_alunno_ministeriale,
                        parenti.id_parente,
                        parenti.codice_fiscale as codice_fiscale_genitore,
                        parenti.cognome as cognome_genitore,
                        parenti.nome as nome_genitore,
                        parenti.email as email_genitore,
                        parenti_studenti.parentela

                        from studenti_completi
                        inner join parenti_studenti on (studenti_completi.id_studente = parenti_studenti.id_studente)
                        inner join parenti on (parenti.id_parente = parenti_studenti.id_parente)

                        where
                        parenti.flag_canc = 0
                        AND parenti_studenti.flag_canc = 0
                        AND studenti_completi.ordinamento = '0'
                        AND codice_alunno_ministeriale != ''
                        AND codice_ministeriale <> 'FAKE'
                        AND parenti_studenti.parentela in ('M','P','T')
                        AND studenti_completi.tipo_indirizzo not in ('7')
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                        AND classe != '-1'
                        AND studenti_completi.esito_corrente_calcolato NOT ILIKE '%RITIRATO%'";

        $arrAlunno = $this->db->query($query);

        foreach ($arrAlunno as $alunno) {
            $id_studente = $alunno['id_studente'];
            $id_parente = $alunno['id_parente'];
            $chiave = $id_studente . '-'.$id_parente;
            $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);

            if ($alunno['parentela'] == "M" || $alunno['parentela'] == "P") {
                $grado_parentela = "G";
            } elseif ($alunno['parentela'] == "T") {
                $grado_parentela = "T";
            }
            if ($alunno['email_genitore'] == '')
            {
                $alunno['email_genitore'] = "-";
            }
            $datiStudenti[$chiave] = [
                "codice_debole_scuola"          => $mecc[1],
                "anno_scolastico"               => $anno_scolastico[0].substr($anno_scolastico[1],2,2),
                "codice_fiscale_genitore"       => $alunno['codice_fiscale_genitore'],
                "nome_genitore"                 => $this->decode($alunno['nome_genitore']),
                "cognome_genitore"              => $this->decode($alunno['cognome_genitore']),
                "grado_parentela"               => $grado_parentela,
                "codice_alunno_ministeriale"    => $alunno['codice_alunno_ministeriale'],
                "email_genitore"                => $alunno['email_genitore'],
            ];
        }
        file_put_contents('/tmp/ds', print_r($datiStudenti,true), FILE_APPEND);
        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiStudenti)) {
                foreach ($datiStudenti as $id => $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiStudenti;
        }
        //}}} </editor-fold>
    }


    /**
     * Verifica se lo studente ha il codice SIDI settato
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student) {
        //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];
        file_put_contents('/tmp/student', print_r($student,true), FILE_APPEND);
        if ($student['grado_parentela'] == '') {
            $errors['errors'] = "Errore: manca la parentela per {$student['cognome_genitore']} {$student['nome_genitore']} codice fiscale: {$student['codice_fiscale_genitore']}.";
        }

        if ($student['codice_alunno_ministeriale'] == '') {
            $errors['errors'] = "Errore: manca il codice sidi al figlio di {$student['cognome_genitore']} {$student['cognome_genitore']} codice fiscale: {$student['codice_fiscale_genitore']}";
        }

        if ($student['codice_fiscale_genitore'] == '' || $student['codice_fiscale_genitore'] == 'NON CALCOLABILE NESSUN RISULTATO' ) {
            $dati_alunno = $this->getStudenteDaCodiceSidi($student['codice_alunno_ministeriale']);
            $errors['errors'] = "Errore per lo studente {$dati_alunno['cognome']} {$dati_alunno['nome']}: manca il codice fiscale per parente {$student['cognome_genitore']} {$student['nome_genitore']}.";
        }
        file_put_contents('/var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/errori.txt', print_r($errors,true), FILE_APPEND);
        return $errors;
        //}}} </editor-fold>
    }


    /**
     * Restituisce il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola, codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
        //}}} </editor-fold>
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4) {
        //{{{ <editor-fold defaultstate="collapsed">
        if (!isset($id) || trim($id) === '') {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++) {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
        //}}} </editor-fold>
    }

    private function associazione_genitore_alunno($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiFinali = $dati_genitore_alunno = [];
        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];

        $timestamp = date('YmdHi');
        $mecc = explode('-', $meccanografico);
        $annoScolatico = $this->getAnnoScolastico();
        $codici_scuola = $this->getCodiciScuola($meccanografico);

        // File Studenti
        $datiStudente = $this->getStudenti($meccanografico);
        foreach ($datiStudente as $id_studente => $studente) {
            $dati_genitore_alunno[$id_studente] = implode("|", $studente) . "|\n";
        }

        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolatico['valore'])[0] .
                        "00AG" .
                        "MAST" .
                        "20231005" .
                        $timestamp;



        $datiFinali = [
            'genitore_alunno' => array_values($dati_genitore_alunno)
        ];


        if (empty($result[$meccanografico]['errors_stop'])) {
            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/*00AGMAST20231005*");

            // Creo il file HST_GENITOREALU
            $filename_scuole = $prefisso_file . '001TB_HST_GENITOREALU.TXT';
            foreach ($datiFinali['genitore_alunno'] as $riga_genitore_alunno) {
                file_put_contents("/var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/" . $filename_scuole, print_r($riga_genitore_alunno, true), FILE_APPEND);
            }


            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/GENALU_' . explode('-',$meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile) {
                if ($zip->open($zipFile) === TRUE) {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/##' . $prefisso_file . '.dat', '##' . $prefisso_file . '.dat')
                            or die("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }
            exec("rm /var/www-source/mastercom/tmp_siis/associazione_genitore_alunno/*00AGMAST20231005*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));
        file_put_contents('/tmp/datiFinali', print_r($datiFinali, true));

        return $result[$meccanografico]['errors_stop'];
        //}}} </editor-fold>
    }
}
