<?php

class SiisFrequenzaInfanzia extends Siis {

    protected $db;
    protected $pdf;

    function __construct() {
        $db = new MT\Mastercom\Db();
        $this->db = $db;

        $pdf = new PDFClass();
        $this->pdf = $pdf;
    }

    function decode($field) {
        if (!is_array($field) && strlen($field) > 0) {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        } else {
            return $field;
        }
    }

    public function normalize_string($string) {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico() {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     * $filename riporta il prefisso dei tre file dell'anagrafe nazionale
     *
     * @return int
     */
    public function createZip($fileName, $codMecc, $timestamp = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;

        exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/AVVIOSCUOLAINFANZIA_{$codMecc}_{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/{$fileName}*.TXT");

        foreach ($glob as $file) {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/AVVIOSCUOLAINFANZIA_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE) {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/*.TXT");
        //}}} </editor-fold>
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $cod
     */
    public function mergeFile($cod) {
        //{{{ <editor-fold defaultstate="collapsed">
        $annoScolatico = $this->getAnnoScolastico();
        $timestamp = date('YmdHi');
        $timestamp2 = date('Ymd');
        $codMecc = explode('-', $cod)[1];
        $path_files = "/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/";

        // creo i file finali
        $codici_scuola = $this->getCodiciScuola($cod);
        $prefisso_file = $codMecc .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolatico['valore'])[0] .
                        "00FI" .
                        "MAST" .
                        "20160617" .
                        $timestamp;

        $glob = glob($path_files . "AVVIOSCUOLAINFANZIA_{$codMecc}_{$timestamp2}*.zip");

        // recupero i due file: quello creato in locale e quello creato sull'altro server
        foreach ($glob as $key => $file) {
            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = $path_files . $basename;

            if ($zip->open($nomeZip) == TRUE) {
                exec("mkdir {$path_files}tmp/{$key}/");

                $zip->extractTo("{$path_files}tmp/{$key}/");
                $zip->close();

                $glob_zip = glob("{$path_files}tmp/{$key}/*");

                foreach ($glob_zip as $file_singolo) {
                    if (strpos($file_singolo, "001TB_USR_AS.TXT") !== false) {
                        // anno scolastico (sovrascrivo in quanto la scuola è una sola)
                        $file_anno_scolastico = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}001TB_USR_AS.TXT", print_r($file_anno_scolastico, true));
                    }

                    if (strpos($file_singolo, "004TB_USR_USER.TXT") !== false) {
                        // scuola (sovrascrivo in quanto la scuola è una sola)
                        $file_scuola = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}004TB_USR_USER.TXT", print_r($file_scuola, true));
                    }

                    if (strpos($file_singolo, "005TB_USR_SEDI.TXT") !== false) {
                        // sedi
                        $file_sedi = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}005TB_USR_SEDI.TXT", print_r($file_sedi, true), FILE_APPEND);
                    }

                    if (strpos($file_singolo, "006TB_ALU_SEZ_ANAG.TXT") !== false) {
                        // anagrafica sezione
                        $file_sezioni = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}006TB_ALU_SEZ_ANAG.TXT", print_r($file_sezioni, true), FILE_APPEND);
                    }

                    if (strpos($file_singolo, "007TB_ALU_ALUNNI.TXT") !== false) {
                        // studenti
                        $file_studenti = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}007TB_ALU_ALUNNI.TXT", print_r($file_studenti, true), FILE_APPEND);
                    }

                    if (strpos($file_singolo, "008TB_ALU_SIT.TXT") !== false) {
                        // curriculum
                        $file_curriculum = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}008TB_ALU_SIT.TXT", print_r($file_curriculum, true), FILE_APPEND);
                    }
                }

                exec("rm {$path_files}tmp/{$key}/*");
            } else {
                echo 'Merge files failed';
            }
        }

        // creo lo zip finale
        $this->createZip($prefisso_file, $codMecc, $timestamp);

        // creo il .dat
        file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/##" . $prefisso_file . ".dat", "");

        $fileZip = glob($path_files . 'AVVIOSCUOLAINFANZIA_' . $codMecc . '_' . $timestamp . '.zip');
        $zip = new ZipArchive;

        foreach ($fileZip as $zipFile) {
            if ($zip->open($zipFile) === TRUE) {
                $zip->addFile('/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                        or die ("ERROR: Could not add file: {$prefisso_file}");
                $zip->close();
            }
        }
        //}}} </editor-fold>
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        // Scuola
        $scuola = $this->getScuola($meccanografico, 'verify');
        if (!empty($scuola['errors'])) {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Scuola',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
        }

        // Sede
        $sede = $this->getSede($meccanografico, 'verify');
        if (!empty($sede['errors']['codice_sidi'])) {
            foreach ($sede['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Sede',
                    'valore' => $value
                ];
            }
        }

        // Sezione
        $anagraficasezione = $this->getAnagraficaSezione($meccanografico, 'verify');
        if (!empty($anagraficasezione['errors']['codice_sidi'])) {
            foreach ($anagraficasezione['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Sezione',
                    'valore' => $value
                ];
            }
        }

        // Studenti
        $studenti = $this->getStudenti($meccanografico, 'verify');
        if (!empty($studenti['errors']['codice_sidi'])) {
            foreach ($studenti['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Studenti',
                    'valore' => $value
                ];
            }
        }

        // Curriculum
        $curriculum = $this->getCurriculum($meccanografico, 'verify');
        if (!empty($curriculum['errors']['codice_sidi'])) {
            foreach ($curriculum['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Curriculum',
                    'valore' => $value
                ];
            }
        }

        if (!empty($result[$meccanografico]['errors_stop'])) {
            return $result[$meccanografico]['errors_stop'];
        } else {
            return [];
        }
        //}}} </editor-fold>
    }

    /**
     * Elabora i file
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function generate($meccanografico) {
        return $this->frequenza_infanzia($meccanografico);
    }

       /**
     * Restituisce i dati dell'anno scolastico
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getDatiAnnoScolastico($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);

        // inizo lezioni
        $query_inizio = "SELECT valore FROM parametri WHERE nome = 'DATA_INIZIO_ANNO_SCOLASTICO' AND flag_canc = 0";
        $inizio =  explode("/", $this->db->query($query_inizio)['valore']);

        // fine lezioni
        $query_fine = "SELECT valore FROM parametri WHERE nome = 'DATA_FINE_ANNO_SCOLASTICO' AND flag_canc = 0";
        $fine =  explode("/", $this->db->query($query_fine)['valore']);

        $datiAS[$codici_scuola['codice_prenotazione']] = [
            "fine"              => $anno_scolastico[1] . "-" . $fine[1] . "-" . $fine[0],
            "inizio"            => $anno_scolastico[0] . "-" . $inizio[1] . "-" . $inizio[0],
            "anno_scolastico"   => $anno_scolastico[0],
            "ordine_scuola"     => $codici_scuola['codice_prenotazione'] . "AA",
        ];

        return $datiAS;
        //}}} </editor-fold>
    }

   /**
     * Restituisce i dati della scuola in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiScuola = $errors = [];

        $mecc = explode('-', $meccanografico);
        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $query = "SELECT DISTINCT
                        sc.id_scuola,
                        sc.codice_prenotazione,
                        sc.codice_meccanografico,
                        sc.codice_meccanografico_secondario,
                        sedi.id_comune
                    FROM scuole sc,
                        sedi,
                        indirizzi,
                        indirizzi_ministeriali
                    WHERE sc.id_scuola = sedi.id_scuola::int
                        AND sedi.id_sede = indirizzi.id_sede::bigint
                        AND indirizzi.id_codice_ministeriale = indirizzi_ministeriali.id_indirizzo
                        AND indirizzi_ministeriali.classificazione IN ('AA')
                        AND indirizzi.tipo_indirizzo IN ('7')
                        AND indirizzi.flag_canc = 0
                        AND sc.flag_canc = 0
                        AND sedi.flag_canc = 0
                        AND indirizzi_ministeriali.flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                    LIMIT 1";

        $arrScuola =  $this->db->query($query);

        // verifica dati
        if (!empty($arrScuola)) {
            if ($arrScuola['codice_prenotazione'] == '') {
                $errors['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
            if ($arrScuola['id_comune'] == '') {
                $errors['id_comune'][] = "Errore: manca il codice del comune per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
        } else {
            $errors['codice_prenotazione'][] = "Dati Scuola mancanti per la scuola {$mecc[0]} - {$mecc[1]}";
        }

        $datiScuola[$arrScuola['id_scuola']] = [
            'codice_meccanografico_debole_scuola'	=>	$arrScuola['codice_meccanografico_secondario'],
            'codice_comune'							=>	$arrScuola['id_comune'],
            'identificativo_scuola' 				=>	$codici_scuola['codice_prenotazione'] . $this->formatId($arrScuola['id_scuola']),
        ];

        if ($check == 'verify' && !empty($errors)) {
            // aggiungo gli eventuali errori
            $datiScuola['errors'] = $errors;
        }

        return $datiScuola;
        //}}} </editor-fold>
    }

    /**
     * Restituisce i dati della scuola in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getSede($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiSede = $errors = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);

        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);
        $datiScuola = $this->getScuola($meccanografico);
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                        cap,
                        id_comune,
                        descrizione,
                        fax,
                        id_sede,
                        indirizzo,
                        id_scuola,
                        telefono,
                        data_inizio_validita,
                        data_fine_validita
                    FROM sedi
                    WHERE sedi.flag_canc = 0
                        AND id_scuola = '{$codici_scuola['id_scuola']}'";

        $arrSede = $this->db->query($query);

        $data_inizio_validita = $arrSede['data_inizio_validita'] ? explode('/', $arrSede['data_inizio_validita']) : ['01', '09', $anno_scolastico[0]];
        $data_fine_validita = $arrSede['data_fine_validita'] ? explode('/', $arrSede['data_fine_validita']) : [];

        // verifica dati
        if (!empty($arrSede)) {
            if ($arrSede['id_comune'] == '') {
                $errors['id_comune'][] = "Errore: manca il codice del comune per la sede della scuola {$arrSede['descrizione']}";
            }
        } else {
            $errors['codice_prenotazione'][] = "Dati Scuola mancanti per la scuola {$mecc[0]} - {$mecc[1]}";
        }

        $datiSede[$arrSede["id_scuola"]] = [
            'cap'                   => $arrSede['cap'],
            'codice_comune'         => $arrSede['id_comune'],
            'denominazione_sede'    => $this->decode($arrSede['descrizione']),
            'fax'                   => $arrSede['fax'],
            'identificativo_sede'   => $codici_scuola['codice_prenotazione'] . $this->formatId($arrSede['id_sede']),
            'indirizzo_sede'        => $arrSede['indirizzo'],
            'identificativo_scuola' => $codici_scuola['codice_prenotazione'] . $this->formatId($arrSede['id_scuola']),
            'telefono'              => $arrSede['telefono'],
            'data_inizio_validita'  => $data_inizio_validita[2] . '-' . $data_inizio_validita[1] . '-' . $data_inizio_validita[0],
            'data_fine_validita'    => !empty($data_fine_validita) ? $data_fine_validita[2] . '-' . $data_fine_validita[1] . '-' . $data_fine_validita[0] : ''
        ];

        if ($check == 'verify') {
            // aggiungo gli eventuali errori
            $datiSede['errors'] = $errors;
        }

        return $datiSede;
        //}}} </editor-fold>
    }

    /**
     * Restituisce i dati della sezioni in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getAnagraficaSezione($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiSezione = $errors = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                    codice_ministeriale,
                    sezione as denominazione_sezione,
                    sezione_sidi as denominazione_sezione_sidi,
                    tempo_funzionamento,
                    id_sede,
                    flag_primavera,
                    giorni_settimanali,
                    CASE WHEN flag_primavera = 1 THEN '0' ELSE '' END AS flag_finanziato
				 FROM classi_complete
				 WHERE  codice_meccanografico = '{$mecc[0]}'
                    AND codice_meccanografico_secondario = '{$mecc[1]}'
                    AND codice_ministeriale != 'FAKE'
                    AND classificazione_indirizzi IN ('AA')
                    AND ordinamento = '0'
				 ORDER BY codice_ministeriale";

        $arrSezione = $this->db->query($query, true);

        // verifica dati
        if (!empty($arrSezione)) {

            if ($arrSezione['denominazione_sezione_sidi'] == '') {
                $errors['denominazione_sezione_sidi'][] = "Errore: manca la denominazione sezione per la scuola {$mecc[0]}-{$mecc[1]}";
            }
        } else {
            $errors['codice_prenotazione'][] = "Dati Scuola mancanti per la scuola {$mecc[0]} - {$mecc[1]}";
        }

        foreach ($arrSezione as $sezione) {
            $tempo_funzionamento = $sezione['tempo_funzionamento'] == 0 ? 20 : $sezione['tempo_funzionamento'];

            $datiSezione[] = [
                'anno_scolastico'       => $anno_scolastico[0],
                'identificativo_scuola' => $codici_scuola['codice_prenotazione'] . $this->formatId($codici_scuola['id_scuola']),
                'identificativo_sede'   => $codici_scuola['codice_prenotazione'] . $this->formatId($sezione['id_sede']),
                'denominazione_sezione' => $sezione['denominazione_sezione_sidi'],
                'tempo_funzionamento'   => $codici_scuola['codice_prenotazione'] . $tempo_funzionamento,
                'flag_primavera'        => $sezione['flag_primavera'],
                'flag_finanziato'       => $sezione['flag_finanziato'],
                'giorni_settimanali'    => $sezione['giorni_settimanali']
            ];
        }

        if ($check == 'verify') {
            // aggiungo gli eventuali errori
            $arrSezione['errors'] = $errors;
        }

        return $datiSezione;
        //}}} </editor-fold>
    }

    /**
     * Restituisce i dati degli alunni in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $errors = [];
        $datiStudenti = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT ON (id_studente)
                        id_studente,
                        data_nascita,
                        codice_alunno_ministeriale,
                        flag_cf_fittizio,
                        cap_residenza,
                        codice_fiscale,
                        cittadinanza,
                        seconda_cittadinanza,
                        codice_comune_nascita,
                        cognome,
                        c.codice_sidi as codice_comune_residenza,
                        indirizzo,
                        matricola,
                        nome,
                        id_scuola,
                        sesso,
                        stato_nascita,
                        citta_nascita_straniera
                    FROM studenti_completi
                    LEFT JOIN comuni c ON studenti_completi.codice_comune_residenza = c.id_comune
                    WHERE codice_ministeriale = 'AA'
                        AND ordinamento = '0'
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                        AND classe != '-1'
                        AND codice_comune_residenza = c.id_comune";

        $arrAlunno = $this->db->query($query);

        foreach ($arrAlunno as $alunno) {
            $id_studente = $alunno['id_studente'];
            $data_nascita = !in_array($alunno['data_nascita'], ['-1', '']) ? date('Y-m-d', $alunno['data_nascita']) : "";

            if ($alunno['seconda_cittadinanza'] == '-1') {
                $alunno['seconda_cittadinanza'] = '';
            }

            $alunno['cittadinanza'] = str_replace("-", "", $alunno['cittadinanza']);
            $alunno['seconda_cittadinanza'] = str_replace("-", "", $alunno['seconda_cittadinanza']);
            $alunno['stato_nascita'] = str_replace("-", "", $alunno['stato_nascita']);
            $alunno['codice_comune_nascita'] = str_replace("-", "", $alunno['codice_comune_nascita']);
            $alunno['citta_nascita_straniera'] = str_replace("-", "", $alunno['citta_nascita_straniera']);

            if ($alunno['stato_nascita'] == '0001') {
                $alunno['stato_nascita'] = '200';
            }

            if ($alunno['cittadinanza'] == '0001') {
                $alunno['cittadinanza'] = '200';
            }

            if ($alunno['seconda_cittadinanza'] == '0001') {
                $alunno['seconda_cittadinanza'] = '200';
            }

            if ($alunno['cittadinanza'] == $alunno['seconda_cittadinanza']) {
                $alunno['seconda_cittadinanza'] = '';
            }

            if ($alunno['descrizione_stato_nascita'] == $alunno['citta_nascita_straniera']) {
                $alunno['citta_nascita_straniera'] = '';
            }

            if ($alunno['codice_comune_nascita'] == 'XXXX') {
                $alunno['codice_comune_nascita'] = '';
            }

            if ($alunno['cittadinanza'] != "") {
                $alunno['cittadinanza'] = (int) $alunno['cittadinanza'];
            }

            if ($alunno['seconda_cittadinanza'] != "") {
                $alunno['seconda_cittadinanza'] = (int) $alunno['seconda_cittadinanza'];
            }

            if ($alunno['stato_nascita'] != "") {
                $alunno['stato_nascita'] = (int) $alunno['stato_nascita'];
            }

            $datiStudenti[$id_studente] = [
                "data_di_nascita"               => $data_nascita,
                "codice_alunno_ministeriale"    => $alunno['codice_alunno_ministeriale'],
                "flag_codice_fiscale_fittizio"  => $alunno['flag_cf_fittizio'],
                "anni_frequenza_scuola_materna" => "0",
                "codice_cap_residenza"          => $alunno['cap_residenza'],
                "codice_fiscale"                => $alunno['codice_fiscale'],
                "codice_prima_cittadinanza"     => $alunno['cittadinanza'],
                "codice_seconda_cittadinanza"   => $alunno['seconda_cittadinanza'],
                "codice_comune_nascita_alunno"  => $alunno['codice_comune_nascita'],
                "cognome_alunno"                => $alunno['cognome'],
                "codice_comune_residenza"       => $alunno['codice_comune_residenza'],
                "identificativo_alunno"         => $codici_scuola['codice_prenotazione'] . $alunno['codice_fiscale'],
                "indirizzo_residenza"           => $alunno['indirizzo'],
                "matricola"                     => $alunno['matricola'],
                "nome"                          => $alunno['nome'],
                "identificativo_scuola"         => $codici_scuola['codice_prenotazione'] . $this->formatId($alunno['id_scuola']),
                "sesso"                         => $alunno['sesso'],
                "stato_di_nascita"              => $alunno['stato_nascita'],
                "luogo_nascita_estero"          => $alunno['citta_nascita_straniera'],
            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiStudenti)) {
                foreach ($datiStudenti as $id => $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiStudenti;
        }
        //}}} </editor-fold>
    }

    /**
     * Restituisce i dati del curriculum degli alunni
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getCurriculum($meccanografico, $check = null) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiCurriculum = $errors = [];

        $mecc = explode('-', $meccanografico);
        $annoScolatico = $this->getAnnoScolastico();
        $codici_scuola = $this->getCodiciScuola($meccanografico);

        // 20180929 Modificata origine sezione: prendiamo la sezione sidi recuperata da IMPORTA DATI ANAGRAFE

        $query_studenti = "SELECT id_studente,
                                codice_fiscale,
                                materia_sostitutiva_religione,
                                materia_sostitutiva_edfisica,
                                esonero_religione,
                                esonero_ed_fisica,
                                registro,
                                servizio_mensa,
                                stato_privatista,
                                obbligo_formativo,
                                id_quadro_orario_sidi,
                                id_scuola,
                                id_sede,
                                sezione as denominazione_sezione,
                                sezione_sidi as denominazione_sezione_sidi
                            FROM studenti_completi,
                                    comuni c
                            WHERE codice_ministeriale = 'AA'
                                AND codice_ministeriale!='FAKE'
                                AND ordinamento = '0'
                                AND classificazione_indirizzi NOT IN ('SE')
                                AND codice_meccanografico = '{$mecc[0]}'
                                AND codice_meccanografico_secondario = '{$mecc[1]}'
                                AND classe != '-1' AND codice_comune_residenza = c.id_comune";

        $arrStudenti = $this->db->query($query_studenti);

        foreach ($arrStudenti as $studente) {
            $id_studente = $studente['id_studente'];

            // Curriculum Studente da storia studenti
            $query_studente = "SELECT *
                        FROM storia_studenti
                        WHERE id_studente = {$id_studente}
                            AND flag_canc = 0
                            AND anno_scolastico = '{$annoScolatico['valore']}'";

            $arrCurrStudente = $this->db->query($query_studente, true);

            //{{{ <editor-fold defaultstate="collapsed" desc="Iscrizione + classe tradotta">
            foreach ($arrCurrStudente as $idx => $singleCurr) {
                //Classe
                switch ($singleCurr['classe']) {
                    //{{{ <editor-fold defaultstate="collapsed" desc="Conversione classi curriculum">
                    case 6:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '1';
                        break;
                    case 7:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '2';
                        break;
                    case 8:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '3';
                        break;
                    case 9:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '1';
                        break;
                    case 10:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '2';
                        break;
                    case 11:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '3';
                        break;
                    case 12:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '4';
                        break;
                    case 13:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '5';
                        break;
                    case 19:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '1';
                        break;
                    case 20:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '2';
                        break;
                    case 21:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '3';
                        break;
                    case 22:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '4';
                        break;
                    case 23:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '5';
                        break;
                    //}}} </editor-fold>
                }

                //Iscrizione
                if (strtoupper($singleCurr['esito']) == 'ISCRITTO') {
                    $dataIscrizione = date('Y-m-d', $singleCurr['data_riferimento']);
                    break;
                }
            }
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Provenienti da altra scuola">
            $arrCurrStudenteReverse = $arrCurrStudente;
            krsort($arrCurrStudenteReverse);
            $curr_prec = [];
            $data_inizio_frequenza = null;

            foreach ($arrCurrStudenteReverse as $singleCurr) {
                if (!empty($curr_prec)) {
                    if ($curr_prec['esito'] == 'Trasferito' && $singleCurr['esito'] == 'Iscritto') {
                        $data_inizio_frequenza = date('Y-m-d', $singleCurr['data_riferimento']);
                    }
                }

                if ($singleCurr['esito'] == 'Trasferito' && $singleCurr['id_scuola'] != '' && $singleCurr['id_scuola'] != 'XXXX') {
                    $scuola_esterna = true;
                    if ($singleCurr['id_scuola'] == $mecc[1]) {
                        $scuola_esterna = false;
                    }
                    if ($scuola_esterna) {
                        $curr_prec = $singleCurr;
                    }
                }
            }
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Curriculum studente">
            $subquery = "SELECT DISTINCT esito, anno_scolastico
                            FROM storia_studenti
                            WHERE id_studente = {$id_studente}
                                AND flag_canc = 0
                                AND id_scuola IN ('" . $mecc[0] . "', '" . $mecc[1] . "', '')
                                AND upper(esito) = 'ISCRITTO'";

            $query = "SELECT COUNT(*) FROM ($subquery) AS progressivo_curr";

            $dati_curriculum = $this->db->query($query);

            $progressivoCurr = str_pad($dati_curriculum[0]['count'], 4, '0', STR_PAD_LEFT);
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Trasferimento o Ritiro">
            $arrUltimaRiga = [];

            if (count($arrCurrStudente) > 0) {
                $subquery = "SELECT DISTINCT esito,
                                    anno_scolastico
                                FROM storia_studenti
                                WHERE id_studente = {$id_studente}
                                    AND flag_canc = 0
                                    AND id_scuola IN ('" . $mecc[0] . "', '" . $mecc[1] . "', '')
                                    AND upper(esito) = 'ISCRITTO'
                                    AND classe IN (" . $arrCurrStudente[0]['classe'] . "," . ($arrCurrStudente[0]['classe'] + 20) . ")";

                $query = "SELECT COUNT(*) FROM ({$subquery}) AS num_iscrizioni_classe";

                $arrNumVolteIscrittoClasse = $this->db->query($query);

                $numVolteIscrittoClasse = $arrNumVolteIscrittoClasse[0]['count'];
            } else {
                $arrUltimaRiga['esito'] = null;
                $arrUltimaRiga['data_riferimento'] = null;
            }

            $trasferito = null;
            $dataTrasferimento = null;

            if (in_array(strtoupper($arrUltimaRiga['esito']), ['TRASFERITO', 'TRASFERITO PRIMA DEL 15/3'])) {
                // Trasferito
                $trasferito = '1';
                $dataTrasferimento = date('Y-m-d', $arrUltimaRiga['data_riferimento']);
            } else if (in_array(strtoupper($arrUltimaRiga['esito']), ['RITIRATO', 'RITIRATO PRIMA DEL 15/3'])) {
                // Ritirato
                $trasferito = '4';
                $dataTrasferimento = date('Y-m-d', $arrUltimaRiga['data_riferimento']);
            }
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Classe dello Studente">
            $query_classe = "SELECT classi_complete.*
                                FROM classi_complete
                                INNER JOIN classi_studenti ON (classi_complete.id_classe = classi_studenti.id_classe)
                                LEFT JOIN tempi_funzionamento ON (classi_complete.tempo_funzionamento = tempi_funzionamento.id_tempo_funzionamento
                                    AND tempi_funzionamento.flag_canc = 0)
                                WHERE classi_studenti.flag_canc = 0
                                    AND codice_ministeriale != 'FAKE'
                                    AND id_studente = {$id_studente}
                                ORDER BY ordinamento,
                                    classe,
                                    sezione,
                                    descrizione_indirizzi";

            $dati_classe_studente = $this->db->query($query_classe);

            $anno_corso = $dati_classe_studente[0]['classe'];
            $ore_settimanali = '';

            if ($dati_classe_studente[0]['tipo_indirizzo'] == '1' && $dati_classe_studente[0]['codice_ministeriale'] == 'LI01') {
                switch ($dati_classe_studente[0]['classe']) {
                    case '4':
                        $anno_corso = 1;
                        break;
                    case '5':
                        $anno_corso = 2;
                        break;
                    case '1':
                        $anno_corso = 3;
                        break;
                    case '2':
                        $anno_corso = 4;
                        break;
                    case '3':
                        $anno_corso = 5;
                        break;
                }
            }

            if (in_array($dati_classe_studente[0]['tipo_indirizzo'], ['4', '6'])) {
                $query_orario = "SELECT fine_fascia
                                    FROM tempi_funzionamento
                                    WHERE flag_canc = 0
                                        AND id_tempo_funzionamento = " . $dati_classe_studente[0]['tempo_funzionamento'];

                $dati_tempo_funzionamento = $this->db->query($query_classe);

                $ore_settimanali = $dati_tempo_funzionamento[0]['fine_fascia'];
            }
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Esoneri Religione ed Educazione Fisica">

            $studente['materia_sostitutiva_religione'] = $studente['materia_sostitutiva_religione'] != -1 ?: null;
            $studente['materia_sostitutiva_edfisica'] = $studente['materia_sostitutiva_edfisica'] != -1 ?: null;

            $query_esonero_religione = "SELECT materie.id_materia,
                                    tipo_materia,
                                    id_studente
                                FROM materie
                                    INNER JOIN classi_prof_materie ON (materie.id_materia = classi_prof_materie.id_materia)
                                    INNER JOIN classi_studenti ON (classi_studenti.id_classe = classi_prof_materie.id_classe)
                                WHERE tipo_materia IN ('RELIGIONE')
                                    AND materie.flag_canc = 0
                                    AND classi_prof_materie.flag_canc = 0
                                    AND classi_studenti.flag_canc = 0
                                    AND id_studente = {$id_studente}";

            $esoneri_studente_religione = $this->db->query($query_esonero_religione);

            // Religione
            if ($esoneri_studente_religione['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 1) {
                $esonero_religione = $esoneri_studente_religione;
            } else {
                $esonero_religione = ['id_studente' => null];
                $studente['materia_sostitutiva_religione'] = null;
            }

            $query_esonero_edfisica = "SELECT materie.id_materia,
                                    tipo_materia,
                                    id_studente
                                FROM materie
                                    INNER JOIN classi_prof_materie ON (materie.id_materia = classi_prof_materie.id_materia)
                                    INNER JOIN classi_studenti ON (classi_studenti.id_classe = classi_prof_materie.id_classe)
                                WHERE tipo_materia IN ('EDUCAZIONE FISICA')
                                    AND materie.flag_canc = 0
                                    AND classi_prof_materie.flag_canc = 0
                                    AND classi_studenti.flag_canc = 0
                                    AND id_studente = {$id_studente}";

            $esoneri_studente_edfisica = $this->db->query($query_esonero_edfisica);

            // Ed. Fisica
            if ($esoneri_studente_edfisica['tipo_materia'] == 'EDUCAZIONE FISICA' && $studente['esonero_ed_fisica'] == 1) {
                $esonero_ed_fisica = $esoneri_studente_edfisica;
            } else {
                $esonero_ed_fisica = ['id_studente' => null];
                $studente['materia_sostitutiva_edfisica'] = null;
            }
            //}}} </editor-fold>


            $dati_stud[$id_studente] = [
                'cognome'           => $studente['cognome'],
                'nome'              => $studente['nome'],
                'codice_fiscale'    => $studente['codice_fiscale'],
            ];

            // Popolamento Array Curriculum
            $datiCurriculum[$id_studente] = [
                "data_trasferimento"     => $dataTrasferimento,
                "ore_settimanali"        => "",
                "identificativo_alunno"  => $codici_scuola['codice_prenotazione'] . $studente['codice_fiscale'],
                "anno_scolastico"        => explode("/", $annoScolatico['valore'])[0],
                "progressivo_curriculum" => $progressivoCurr,
                "identificativo_scuola"  => $codici_scuola['codice_prenotazione'] . $this->formatId($studente['id_scuola']),
                "data_inizio_frequenza"  => $data_inizio_frequenza,
                "identificativo_sede"    => $codici_scuola['codice_prenotazione'] . $this->formatId($studente['id_sede']),
                "denominazione_sezione"  => $studente['denominazione_sezione_sidi'],
                "flag_trasferito"        => $trasferito,
            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiCurriculum)) {
                foreach ($datiCurriculum as $id => $alunno)
                {
                    $checkCurriculum = $this->checkCurriculumData($dati_stud[$id], $alunno);

                    if (!empty($checkCurriculum['errors']))
                    {
                        $errors['errors']['codice_sidi'][] = $checkCurriculum['errors'];
                    }
                }
            }
        }
        if ($check == 'verify') {
            // aggiungo gli eventuali errori
            $datiCurriculum['errors'] = $errors;
        }

        return $datiCurriculum;
        //}}} </editor-fold>
    }

    /**
     * Verifica se lo studente ha il codice SIDI settato
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student) {
        //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];

        if ($student['data_di_nascita'] == '') {
            $errors['errors'] = "Errore: manca la data di nascita per {$student['cognome']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}.";
        }

        if ($student['codice_cap_residenza'] == '') {
            $errors['errors'] = "Errore: manca il cap di residenza per {$student['cognome']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}.";
        }

        if ($student['indirizzo_residenza'] == '') {
            $errors['errors'] = "Errore: manca l'indirizzo di residenza per {$student['cognome']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}.";
        }

        if ($student['codice_prima_cittadinanza'] == '') {
            $errors['errors'] = "Errore: manca la cittadinanza per {$student['cognome']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}.";
        }

        if ($student['data_di_nascita'] == '') {
            $errors['errors'] = "Errore: manca la data di nascita per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if ($student['stato_nascita'] != "" && strlen($student['stato_nascita']) != 3)
        {
            $errors['errors'] = "Errore: stato di nascita errato per {$student['cognome_alunno']} {$student['nome']}";
        }

        if ($student['cittadinanza'] != "" && strlen($student['cittadinanza']) != 3)
        {
            $errors['errors'] = "Errore: prima cittadinanza errata per {$student['cognome_alunno']} {$student['nome']}";
        }

        if ($student['codice_comune_residenza'] == '' || $student['codice_comune_residenza'] == 'XXXX') {
            $errors['errors'] = "Errore: manca il comune di residenza per {$student['cognome']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}.";
        }

        if ($student['codice_fiscale'] == '' || $student['codice_fiscale'] == 'NON CALCOLABILE NESSUN RISULTATO' ) {
            $errors['errors'] = "Errore: manca il codice fiscale per {$student['cognome']} {$student['nome']}.";
        }

        return $errors;
        //}}} </editor-fold>
    }

    /**
     * Verifica se i curriculum hanno i dati corretti
     *
     * @param array $student
     * @return string
     */
    public function checkCurriculumData($studente, $cv) {
        $errors = ['errors' => ''];

        if ($cv['denominazione_sezione'] == '') {
            $errors['errors'] = "Errore: manca la sezione per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}";
        }

        if (strlen($cv['denominazione_sezione']) > 25) {
            $errors['errors'] = "Errore: denominazione sezione troppo lunga per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}.<br>La lunghezza massima consentita è 25 caratteri.";
        }

        if ($cv['identificativo_sede'] == '') {
            $errors['errors'] = "Errore: sede non associata allo studente {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} - Verificare che il nome della classe sia identico tra Sidi e MasterCom.";
        }

        if ($cv['progressivo_curriculum'] == '') {
            $errors['errors'] = "Errore: nessuna riga di curriculum presente per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}";
        }

        return $errors;
    }

    /**
     * Restituisce il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola, codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
        //}}} </editor-fold>
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4) {
        //{{{ <editor-fold defaultstate="collapsed">
        if (!isset($id) || trim($id) === '') {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++) {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
        //}}} </editor-fold>
    }

    private function frequenza_infanzia($meccanografico) {
        //{{{ <editor-fold defaultstate="collapsed">
        $datiFinali = $dati_finali_as = $dati_finali_scuole = $dati_finali_sedi = $dati_finali_sezioni = $dati_finali_studenti = $dati_finali_curriculum = [];
        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];

        $timestamp = date('YmdHi');
        $mecc = explode('-', $meccanografico);
        $annoScolatico = $this->getAnnoScolastico();
        $codici_scuola = $this->getCodiciScuola($meccanografico);

        // File Anno Scolastico
        $datiAnnoScolastico = $this->getDatiAnnoScolastico($meccanografico);
        foreach ($datiAnnoScolastico as $id_as => $anno_scolastico) {
            $dati_finali_as[$id_as] = implode("|", $anno_scolastico) . "|\n";
        }

        // File Scuola
        $datiScuola = $this->getScuola($meccanografico);
        foreach ($datiScuola as $id_scuola => $scuola) {
            $dati_finali_scuole[$id_scuola] = implode("|", $scuola) . "|\n";
        }

        // File Sedi
        $datiSedi = $this->getSede($meccanografico);
        foreach ($datiSedi as $id_scuola => $sede) {
            $dati_finali_sedi[$id_scuola] = implode("|", $sede) . "|\n";
        }

        // File Sezioni
        $datiSezioni = $this->getAnagraficaSezione($meccanografico);
        foreach ($datiSezioni as $id_sezione => $sezione) {
            $dati_finali_sezioni[$id_sezione] = implode("|", $sezione) . "|\n";
        }

        // File Studenti
        $datiStudente = $this->getStudenti($meccanografico);
        foreach ($datiStudente as $id_studente => $studente) {
            $dati_finali_studenti[$id_studente] = implode("|", $studente) . "|\n";
        }

        // File Curriculum
        $datiCurriculum = $this->getCurriculum($meccanografico);
        foreach ($datiCurriculum as $id_curriculum => $curriculum) {
            $dati_finali_curriculum[$id_curriculum] = implode("|", $curriculum) . "|\n";
        }

        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolatico['valore'])[0] .
                        "00FI" .
                        "MAST" .
                        "20160617" .
                        $timestamp;

        $datiFinali = [
            'anno_scolastico' => array_values($dati_finali_as),
            'scuole'          => array_values($dati_finali_scuole),
            'sedi'            => array_values($dati_finali_sedi),
            'sezioni'         => array_values($dati_finali_sezioni),
            'studenti'        => array_values($dati_finali_studenti),
            'curriculum'      => array_values($dati_finali_curriculum),
        ];


        if (empty($result[$meccanografico]['errors_stop'])) {
            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/*00FIMAST20160617*");

            // Creo il file Anno Scolastico
            $filename_scuole = $prefisso_file . '001TB_USR_AS.TXT';
            foreach ($datiFinali['anno_scolastico'] as $riga_anno_scolastico) {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/" . $filename_scuole, print_r($riga_anno_scolastico, true), FILE_APPEND);
            }

            // Creo il file Scuole
            $filename_scuole = $prefisso_file . '004TB_USR_USER.TXT';
            foreach ($datiFinali['scuole'] as $riga_scuola) {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/" . $filename_scuole, print_r($riga_scuola, true), FILE_APPEND);
            }

            // Creo il file Sedi
            $filename_scuole = $prefisso_file . '005TB_USR_SEDI.TXT';
            foreach ($datiFinali['sedi'] as $riga_sedi) {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/" . $filename_scuole, print_r($riga_sedi, true), FILE_APPEND);
            }

            // Creo il file Anagrafica Sezione
            $filename_scuole = $prefisso_file . '006TB_ALU_SEZ_ANAG.TXT';
            foreach ($datiFinali['sezioni'] as $riga_sezioni) {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/" . $filename_scuole, print_r($riga_sezioni, true), FILE_APPEND);
            }

            // Creo il file Studenti
            $filename_studenti = $prefisso_file . '007TB_ALU_ALUNNI.TXT';
            foreach ($datiFinali['studenti'] as $riga_studente) {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/" . $filename_studenti, print_r($riga_studente, true), FILE_APPEND);
            }

            // Creo il file Curriculum
            $filename_curriculum = $prefisso_file . '008TB_ALU_SIT.TXT';
            foreach ($datiFinali['curriculum'] as $riga_curriculum) {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/" . $filename_curriculum, print_r($riga_curriculum, true), FILE_APPEND);
            }

            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/AVVIOSCUOLAINFANZIA_' . explode('-', $meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile) {
                if ($zip->open($zipFile) === TRUE) {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/##' . $prefisso_file . '.dat', '##' . $prefisso_file . '.dat')
                            or die("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/##*00FIMAST20160617*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));
        file_put_contents('/tmp/datiFinali', print_r($datiFinali, true));

        return $result[$meccanografico]['errors_stop'];
        //}}} </editor-fold>
    }
}
