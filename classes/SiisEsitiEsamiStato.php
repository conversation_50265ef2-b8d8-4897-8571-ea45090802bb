<?php

class SiisEsitiEsamiStato extends Siis
{

    protected $db;
    protected $pdf;

    function __construct($flusso, $database)
    {
        $this->flusso = $flusso;
        $this->database = $database;

        parent::__construct($this->flusso, $this->database);

        $pdf = new PDFClass();
        $this->pdf = $pdf;
    }

    function decode($field)
    {
        if (!is_array($field) && strlen($field) > 0)
        {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        }
        else
        {
            return $field;
        }
    }

    public function normalize_string($string)
    {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     * @return int
     */
    public function createZip($fileName, $codMecc)
    {
        $timestamp2 = date('Ymd');
        $timestamp = date('YmdHi');
        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/VALUTAZIONI_{$codMecc}_{$timestamp2}*.zip");

        $glob = glob("/var/www-source/mastercom/tmp_siis/esiti_esami_stato/{$fileName}.TXT");

        foreach ($glob as $file)
        {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/esiti_esami_stato/VALUTAZIONI_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE)
            {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/*001TB_ALU_ESI_FIN*.TXT");
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $cod
     */
    public function mergeFile($cod)
    {
        $annoScolatico = $this->getAnnoScolastico();
        $timestamp = date('YmdHi');
        $timestamp2 = date('Ymd');
        $codMecc = explode('-', $cod)[1];

        $glob = glob("/var/www-source/mastercom/tmp_siis/esiti_esami_stato/VALUTAZIONI_{$codMecc}_{$timestamp}*.zip");

        foreach ($glob as $key => $file)
        {
            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/esiti_esami_stato/{$basename}";

            if ($zip->open($nomeZip) == TRUE)
            {
                if ($key == 1)
                {
                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/');
                    $zip->close();

                    $txt1 = glob("/var/www-source/mastercom/tmp_siis/esiti_esami_stato/*{$codMecc}*.TXT");
                    file_put_contents('/tmp/txt1', print_r($txt1, true));
                    foreach ($txt1 as $value)
                    {
                        $basename1 = basename($value);
                        exec("mv /var/www-source/mastercom/tmp_siis/esiti_esami_stato/{$basename1} /var/www-source/mastercom/tmp_siis/esiti_esami_stato/{$basename1}_1.TXT");
                    }
                }
                else
                {
                    exec("mkdir /var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/");

                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/');
                    $zip->close();
                }
            }
            else
            {
                echo 'Merge files failed';
            }
        }

        $txt = glob("/var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/*{$codMecc}*.TXT");

        foreach ($txt as $value)
        {
            $basename = basename($value);
            exec("mv /var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/{$basename} /var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/{$basename}_2.TXT");
            exec("mv /var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/{$basename}_2.TXT /var/www-source/mastercom/tmp_siis/esiti_esami_stato/");
        }

        $txt2 = glob("/var/www-source/mastercom/tmp_siis/esiti_esami_stato/*{$codMecc}*.TXT");

        $scuola = $this->getScuola($cod);

        if (array_key_exists(0, $scuola))
        {
            foreach ($scuola as $value)
            {
                $codice_prenotazione = $value['codice_prenotazione'];
                $idScuola = $this->formatId($value['id_scuola']);
            }
        }
        else
        {
            $codice_prenotazione = $scuola['codice_prenotazione'];
            $idScuola = $this->formatId($scuola['id_scuola']);
        }

        $finalName = $codMecc.$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESES'.'MAST'.'20180621'.$timestamp.'001VALUT';

        foreach ($txt2 as $value)
        {
            $file = file_get_contents($value);
            file_put_contents("/var/www-source/mastercom/tmp_siis/esiti_esami_stato/{$finalName}.TXT", print_r($file, true), FILE_APPEND);
        }

        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/*{$codMecc}*.TXT_*.TXT");
        exec("mv /var/www-source/mastercom/tmp_siis/esiti_esami_stato/VALUTAZIONI_{$codMecc}_*.zip /var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/");

        $this->createZip($finalName, $codMecc);

        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/##*ESESMAST20180621*.dat");
        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/##*ESESMAST20180621*.dat");
        $datName = '##'.$codMecc.$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESES'.'MAST'.'20180621'.$timestamp;
        file_put_contents('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/'.$datName.'.dat', print_r('', true));

        $fileZip = glob('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/VALUTAZIONI_'.$codMecc.'_'.$timestamp.'.zip');
        $zip = new ZipArchive;

        foreach ($fileZip as $zipFile)
        {
            if ($zip->open($zipFile) === TRUE)
            {
                $zip->addFile('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/'. $datName. '.dat', $datName.'.dat')  or die ("ERROR: Could not add file: {$datName}");
                $zip->close();
            }
        }

        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/##*ESESMAST20180621*.dat");
        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/*.TXT");
        exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/tmp/*.zip");
    }

    /**
     * Restituisce i dati della scuola in questione
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico)
    {
        $errors = ['errors' => ''];
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                            scuole.id_scuola,
                            scuole.descrizione,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            scuole.codice_prenotazione,
                            sedi.id_sede,
                            sedi.descrizione as sedi_descrizione
                        FROM scuole, sedi, indirizzi
                        WHERE scuole.id_scuola = sedi.id_scuola::int
                            AND sedi.id_sede = indirizzi.id_sede::bigint
                            AND indirizzi.id_codice_ministeriale NOT IN (
                                    SELECT id_indirizzo FROM indirizzi_ministeriali
                                    WHERE classificazione IN ('AA', 'EE')
                                )
                            AND indirizzi.tipo_indirizzo NOT IN ('6','7')
                            AND indirizzi.flag_canc = 0
                            AND scuole.flag_canc = 0
                            AND sedi.flag_canc = 0
                            AND codice_meccanografico = '{$mecc[0]}'
                            AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $datiScuola =  $this->db->query($query);

        if (array_key_exists(0, $datiScuola))
        {
            foreach ($datiScuola as $value)
            {
                if ($value['codice_prenotazione'] == '')
                {
                    $errors['errors']['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$value['codice_meccanografico']}-{$value['codice_meccanografico_secondario']}";

                    if (!empty($errors['errors']))
                    {
                        $datiScuola['errors'] = $errors['errors'];
                    }
                }
            }
        }
        else
        {
            if ($datiScuola['codice_prenotazione'] == '')
            {
                $errors['errors']['codice_prenotazione'] = "Errore: manca il codice prenotazione per la scuola {$datiScuola['codice_meccanografico']}-{$datiScuola['codice_meccanografico_secondario']}";

                if (!empty($errors['errors']))
                {
                    $datiScuola['errors'] = $errors['errors'];
                }
            }
        }

        return $datiScuola;
    }

    /**
     * Verifica se lo studente ha il codice SIDI settato
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student)
    {
        $errors = ['errors' => ''];

        if ($student['codice_alunno_ministeriale'] == '')
        {
            $errors['errors'] = "Errore: manca il codice SIDI per {$student['cognome']} {$student['nome']} classe {$student['classe']}{$student['sezione']}. "
            . "<br> Per scaricare il codice SIDI andare in Setup C06 - ESTRAZIONE ALUNNI SOGEI, inserire le credenziali, selezionare l'istituto e premere su Procedere";
        }

        return $errors;
    }

    /**
     * Estrae il dato riferito al parametro TIPO_VISUALIZZAZIONE_MINUTI_ASSENZA
     *
     * @return int
     */
    public function getPeriodi()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'TIPO_VISUALIZZAZIONE_MINUTI_ASSENZA'";
        return $this->db->query($query);
    }


    /**
     * Estrae il dato riferito al parametro SCALA_VOTI
     *
     * @return int
     */
    public function getScalaVoti()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'SCALA_VOTI'";
        $arrScalaVoti = $this->db->query($query);
        return $arrScalaVoti['valore'];
    }

    /**
     * Estrae l'elenco degli studenti per la scuola in questione con i seguenti
     * filtri:
     *
     * codice_ministeriale <> 'FAKE' (Indirizzo non FAKE)
     * classi 1,2,3 per indirizzi Q4
     * classi 1,2 per indirizzo Q3
     * esito <> Trasferito e Ritirato
     *
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null)
    {
        $errors = ['errors' => ''];

        $annoScolatico = $this->getAnnoScolastico();
        $asI = explode('/', $annoScolatico['valore'])[0];
        $asF = explode('/', $annoScolatico['valore'])[1];

        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                            id_studente,
                            cognome,
                            nome,
                            codice_fiscale,
                            classe,
                            sezione,
                            sezione_sidi,
                            codice_indirizzi,
                            tipo_indirizzo,
                            esito_corrente_calcolato,
                            presente_esame_quinta,
                            voto_primo_scritto,
                            tipo_primo_scritto,
                            voto_secondo_scritto,
                            voto_terzo_scritto,
                            tipo_terzo_scritto,
                            voto_orale,
                            data_orale,
                            voto_bonus,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            crediti_finali_agg,
                            motivi_crediti_terza,
                            motivi_crediti_quarta,
                            motivi_crediti_quinta,
                            motivi_crediti_agg,
                            ulteriori_specif_diploma,
                            lode,
                            codice_alunno_ministeriale,
                            ulteriori_specif_diploma,
                            unanimita_primo_scritto,
                            unanimita_secondo_scritto,
                            unanimita_terzo_scritto,
                            unanimita_voto_finale,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            descrizione_indirizzi
                        FROM studenti_completi
                        WHERE ammesso_esame_quinta = 'SI'
                            AND codice_ministeriale <> 'FAKE'
                            AND codice_meccanografico = '{$mecc[0]}'
                            AND codice_meccanografico_secondario = '{$mecc[1]}'
                            AND classe ='4'
                            AND tipo_indirizzo = '5'
                            AND ordinamento = '0'
                            AND esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND esito_corrente_calcolato NOT ILIKE '%estero%'

                UNION

                    SELECT DISTINCT
                            id_studente,
                            cognome,
                            nome,
                            codice_fiscale,
                            classe,
                            sezione,
                            sezione_sidi,
                            codice_indirizzi,
                            tipo_indirizzo,
                            esito_corrente_calcolato,
                            presente_esame_quinta,
                            voto_primo_scritto,
                            tipo_primo_scritto,
                            voto_secondo_scritto,
                            voto_terzo_scritto,
                            tipo_terzo_scritto,
                            voto_orale,
                            data_orale,
                            voto_bonus,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            crediti_finali_agg,
                            motivi_crediti_terza,
                            motivi_crediti_quarta,
                            motivi_crediti_quinta,
                            motivi_crediti_agg,
                            ulteriori_specif_diploma,
                            lode,
                            codice_alunno_ministeriale,
                            ulteriori_specif_diploma,
                            unanimita_primo_scritto,
                            unanimita_secondo_scritto,
                            unanimita_terzo_scritto,
                            unanimita_voto_finale,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            descrizione_indirizzi
                        FROM studenti_completi
                        WHERE ammesso_esame_quinta = 'SI'
                            AND codice_ministeriale <> 'FAKE'
                            AND codice_meccanografico = '{$mecc[0]}'
                            AND codice_meccanografico_secondario = '{$mecc[1]}'
                            AND classe = '3'
                            AND tipo_indirizzo = '1'
                            AND ordinamento = '0'
                            AND esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND esito_corrente_calcolato NOT ILIKE '%estero%'

                UNION

                    SELECT DISTINCT
                            id_studente,
                            cognome,
                            nome,
                            codice_fiscale,
                            classe,
                            sezione,
                            sezione_sidi,
                            codice_indirizzi,
                            tipo_indirizzo,
                            esito_corrente_calcolato,
                            presente_esame_quinta,
                            voto_primo_scritto,
                            tipo_primo_scritto,
                            voto_secondo_scritto,
                            voto_terzo_scritto,
                            tipo_terzo_scritto,
                            voto_orale,
                            data_orale,
                            voto_bonus,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            crediti_finali_agg,
                            motivi_crediti_terza,
                            motivi_crediti_quarta,
                            motivi_crediti_quinta,
                            motivi_crediti_agg,
                            ulteriori_specif_diploma,
                            lode,
                            codice_alunno_ministeriale,
                            ulteriori_specif_diploma,
                            unanimita_primo_scritto,
                            unanimita_secondo_scritto,
                            unanimita_terzo_scritto,
                            unanimita_voto_finale,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            descrizione_indirizzi
                        FROM studenti_completi
                        WHERE ammesso_esame_quinta = 'SI'
                            AND codice_ministeriale <> 'FAKE'
                            AND codice_meccanografico = '{$mecc[0]}'
                            AND codice_meccanografico_secondario = '{$mecc[1]}'
                            AND classe = '5'
                            AND tipo_indirizzo IN ('0', '2', '3')
                            AND ordinamento = '0'
                            AND esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND esito_corrente_calcolato NOT ILIKE '%estero%'
                ORDER BY codice_indirizzi, classe, sezione, cognome, nome
                           ";

        $arrAlunno = $this->db->query($query);

        if ($check)
        {
            if (is_array($arrAlunno))
            {
                foreach ($arrAlunno as $alunno)
                {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors']))
                    {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
            else
            {
                $errors['errors']['nessun_dato_presente'][] = "Non sono presenti dati da elaborare per la scuola {$mecc[0]}-{$mecc[1]}";
            }
        }

        if (!empty($errors['errors']))
        {
            return $errors;
        }
        else
        {
            return $arrAlunno;
        }
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4)
    {
        if (!isset($id) || trim($id) === '')
        {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++)
        {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    }

    private function valutazioni($meccanografico)
    {
        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];
        $datiFlusso = [];

        $annoScolatico = $this->getAnnoScolastico();
        $scuola = $this->getScuola($meccanografico);
        $datiStudente = $this->getStudenti($meccanografico);

        foreach ($datiStudente as $studente)
        {
            $chiaveDatiFlusso = $studente['classe'] . ' ' . $studente['sezione'] . ' ' . $studente['cognome'] . ' ' . $studente['nome'] . ' - ' . $studente['codice_fiscale'];

            // Calcoli crediti e voti
            $tot_crediti_formativi = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'] +
                                     $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'] +
                                     $studente['crediti_quinta'];

            $voto_finale = $studente['voto_primo_scritto'] +
                           $studente['voto_secondo_scritto'] +
                           $studente['voto_orale'] +
                           $studente['crediti_finali_agg'] +
                           $studente['voto_bonus'] +
                           $tot_crediti_formativi;

            if ($studente['voto_primo_scritto'] +
                           $studente['voto_secondo_scritto'] +
                           $studente['voto_orale'] >= 50 && $tot_crediti_formativi >= 30)
            {
                $bonus = $studente['crediti_finali_agg'] > 0 ? $studente['crediti_finali_agg'] : $studente['voto_bonus'];
            }
            else
            {
                $bonus = 0;
            }

            // Presenza e azzeramento crediti in caso di studenti assenti all'esame
            if ($studente['presente_esame_quinta'] == 'SI' )
            {
                $presenza = "P";
            }
            else
            {
                $presenza = "I";
                $tot_crediti_formativi = 0;
                $voto_finale = 0;
            }

            // lode
            $studente['lode'] = substr($studente['lode'], 0, -1);

            // Tipologia candidato (da curriculum)
            $query_ultimo_curr = "SELECT tipo_studente FROM storia_studenti WHERE id_studente = " . $studente['id_studente'] .
                                    " AND flag_canc = 0 ORDER BY anno_scolastico DESC, data_riferimento DESC LIMIT 1";
            $arrCurr = $this->db->query($query_ultimo_curr);
            $tipologia_candidato = $arrCurr[0]['tipo_studente'] == 'P' ? "E" : "I";

            // Codice Commissione
            $query_commissione = "SELECT codice_ministeriale, codice_meccanografico FROM commissioni WHERE flag_canc = 0 AND id_classe IN " .
                                    "(SELECT id_classe FROM studenti_completi WHERE id_studente = " . $studente['id_studente'] . ")";
            $arrComm = $this->db->query($query_commissione);

            if (strlen($arrComm['codice_meccanografico']) != 9)
            {
                $codice_commissione = $arrComm['codice_ministeriale'];
            }
            else
            {
                $codice_commissione = $arrComm['codice_meccanografico'];
            }

            $credito_documentato = $studente["motivi_crediti_terza"] . " " . $studente["motivi_crediti_quarta"] . " " . $studente["motivi_crediti_quinta"] . " " . $studente["motivi_crediti_agg"];
            $credito_documentato = $this->normalize_string($credito_documentato);
            $ulteriori_spec = $this->normalize_string($studente['ulteriori_specif_diploma']);

            // Progressione studi
            $query_progressione = "SELECT progressione_studi FROM classi_complete " .
                                    "INNER JOIN classi_studenti ON (classi_complete.id_classe = classi_studenti.id_classe)" .
                                    " WHERE id_studente = " . $studente['id_studente'] . "AND progressione_studi != ''";

            $arrProg = $this->db->query($query_progressione, true);
            $progressione_studi = '';

            foreach($arrProg as $progressione)
            {
                $progressione_studi = trim($progressione['progressione_studi']) != "" ? $this->normalize_string($progressione['progressione_studi']) : $progressione_studi;
            }

            // Unanimità varie
            $unanimita_primo_scritto = $studente['unanimita_primo_scritto'] == 'U' ? "S" : "N";
            $unanimita_secondo_scritto = $studente['unanimita_secondo_scritto'] == 'U' ? "S" : "N";
            $unanimita_terzo_scritto = $studente['unanimita_terzo_scritto'] == 'U' ? "S" : "N";
            $unanimita_voto_orale = $studente['unanimita_voto_finale'] == 'U' ? "S" : "N";

            //estrazione tipo materia secondo scritto
            $query_tipo_materia = "SELECT materia_secondo_scritto FROM classi_complete" .
                                    " INNER JOIN classi_studenti ON (classi_studenti.id_classe = classi_complete.id_classe)" .
                                    " WHERE id_studente = " . $studente['id_studente'] . " AND materia_secondo_scritto != ''";

            $arrMateriaDiploma = $this->db->query($query_tipo_materia);
            if (strtoupper(strpos($studente['descrizione_indirizzi'], 'SCIENT')))
            {
                $tipo_seconda_prova = $studente['tipo_secondo_scritto'];
            }
            else
            {
                $tipo_seconda_prova = "";
            }

            switch(strtoupper(trim($arrMateriaDiploma[0]['materia_secondo_scritto'])))
            {
                case "MATEMATICA":
                    $tipo_prova_matematica = in_array(strtoupper(trim($studente['tipo_secondo_scritto'])), ['2','II']) ? "II" : "I";
                    $tipo_prova_lingua_straniera = "";
                    break;
                case "LINGUA STRANIERA":
                    switch (true)
                    {
                        case stristr($studente['tipo_secondo_scritto'], 'inglese'):
                            $tipo_prova_lingua_straniera = 'IN';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'francese'):
                            $tipo_prova_lingua_straniera = 'FR';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'russo'):
                            $tipo_prova_lingua_straniera = 'RU';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'spagnolo'):
                            $tipo_prova_lingua_straniera = 'SP';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'tedesco'):
                            $tipo_prova_lingua_straniera = 'TE';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'arabo'):
                            $tipo_prova_lingua_straniera = 'AR';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'cinese'):
                            $tipo_prova_lingua_straniera = 'CI';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'ebraico'):
                            $tipo_prova_lingua_straniera = 'EB';
                            break;
                        case stristr($studente['tipo_secondo_scritto'], 'giapponese'):
                            $tipo_prova_lingua_straniera = 'GI';
                            break;
                    }
                    $tipo_prova_matematica = "";
                    break;
                default:
                    $tipo_prova_matematica = "";
                    $tipo_prova_lingua_straniera = "";
                    break;
            }

            // Data Delibera della commissione (estratta dalla riga di Curriculum)
            $query_cv_delibera = "SELECT data_riferimento AS data_delibera FROM storia_studenti WHERE id_studente = " . $studente['id_studente'] .
                                    " AND flag_canc = 0 AND esito ilike '%diplomato%' ORDER BY anno_scolastico DESC, data_riferimento DESC LIMIT 1";
            $arrDelibera = $this->db->query($query_cv_delibera);

            //$data_delibera_commissione = date('d/m/Y', $arrDelibera['data_delibera']);
            $data_delibera_commissione = date('d/m/Y', $arrDelibera['data_delibera']) == '01/01/1970' ? date('d/m/Y', $studente['data_orale']) : date('d/m/Y', $arrDelibera['data_delibera']);

            // Imposto il mese della sessione d'esame (6,7,8,9)
            $mese_orale = date('d/m/Y', $studente['data_orale']) == '01/01/1970' ? date('n', $arrDelibera['data_delibera']) : date('n', $studente['data_orale']);

            if (in_array($mese_orale, [6, 7, 8, 9]))
            {
                $mese_sessione_esame = $mese_orale;
            }
            else
            {
                $mese_sessione_esame = 0;
            }

            // Imposto default 1 in quanto obbligatori per i tecnici ed i professionali
            // ATTENZIONE! Implementare negli esami la possibilità di selezionare i quesiti della seconda prova!
            $seconda_prova_quesito_uno = "1";
            $seconda_prova_quesito_due = "2";

            // array finale flusso
            $datiFlusso[$chiaveDatiFlusso][] = [
                explode('/', $annoScolatico['valore'])[0] . "|",
                explode('-', $meccanografico)[1] . "|",
                $studente['codice_fiscale'] . "|",
                $presenza . "|",
                "N|",
                "N|",
                $studente['voto_primo_scritto'] . "|",
                $studente['tipo_primo_scritto'] . "|",
                $studente['voto_secondo_scritto'] . "|",
                "|",
                "|",
                $studente['voto_orale'] . "|",
                $bonus . "|",
                $voto_finale . "|",
                $studente['lode'] . "|",
                $studente['codice_alunno_ministeriale'] . "|",
                $tipologia_candidato . "|",
                $codice_commissione . "|",
                trim($ulteriori_spec) . "|",
                trim($progressione_studi) . "|",
                $unanimita_primo_scritto . "|",
                $unanimita_secondo_scritto . "|",
                "|",
                $tipo_seconda_prova. "|",
                $tipo_prova_lingua_straniera . "|",
                $unanimita_voto_orale . "|",
                $data_delibera_commissione . "|",
                $mese_sessione_esame . "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                "|",
                ];

        }

        $datiFinali = array_values($datiFlusso);

        if (empty($result[$meccanografico]['errors_stop']))
        {
            $result[$meccanografico]['correct'] = 'File elaborato correttamente';

            $timestamp = date('YmdHi');

            if (array_key_exists(0, $scuola))
            {
                foreach ($scuola as $value)
                {
                    $codice_prenotazione = $value['codice_prenotazione'];
                    $idScuola = $this->formatId($value['id_scuola']);
                }
            }
            else
            {
                $codice_prenotazione = $scuola['codice_prenotazione'];
                $idScuola = $this->formatId($scuola['id_scuola']);
            }

            $fileName = explode('-',$meccanografico)[1].$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESES'.'MAST'.'20180621'.$timestamp.'001VALUT';

            file_put_contents('/var/www-source/mastercom/tmp_siis/esiti_esami_stato'."/{$fileName}.TXT", 'MAST|ESES|20180621|'."\n", FILE_APPEND);

            foreach ($datiFinali as $righe)
            {
                foreach ($righe as $riga)
                {
                    foreach ($riga as $key => $value)
                    {
                        $value .= $key == 39 ? "\n" : "";
                        file_put_contents('/var/www-source/mastercom/tmp_siis/esiti_esami_stato'."/{$fileName}.TXT", print_r($value, true), FILE_APPEND);
                    }
                }
            }

            $this->createZip($fileName, explode('-',$meccanografico)[1]);

            exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/##*ESESMAST20180621*.dat");
            $datName = '##'.explode('-',$meccanografico)[1].$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESES'.'MAST'.'20180621'.$timestamp;
            file_put_contents('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/'.$datName.'.dat', print_r('', true));

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/VALUTAZIONI_'.explode('-',$meccanografico)[1].'_'.$timestamp.'.zip');
            $zip = new ZipArchive;

            foreach ($fileZip as $zipFile)
            {
                if ($zip->open($zipFile) === TRUE)
                {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/esiti_esami_stato/'. $datName. '.dat', $datName.'.dat')  or die ("ERROR: Could not add file: {$datName}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/##*ESESMAST20180621*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));
        file_put_contents('/tmp/datiFinali', print_r($datiFinali, true));

        return $result[$meccanografico]['errors_stop'];
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico)
    {
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        $scuola = $this->getScuola($meccanografico);

        if (!empty($scuola['errors']))
        {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Codice di Prenotazione',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
        }

        $datiStudente = $this->getStudenti($meccanografico, 'check');

        if (!empty($datiStudente['errors']))
        {
            if (!empty($datiStudente['errors']['nessun_dato_presente']))
            {
                foreach ($datiStudente['errors']['nessun_dato_presente'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Nessun dato presente',
                        'valore' => $value
                    ];
                }

                return $result[$meccanografico]['errors_stop'];
            }

            if (!empty($datiStudente['errors']['codice_sidi']))
            {
                foreach ($datiStudente['errors']['codice_sidi'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Codice Sidi',
                        'valore' => $value
                    ];
                }
            }
        }

        if (!empty($result[$meccanografico]['errors_stop']))
        {
            return $result[$meccanografico]['errors_stop'];
        }
        else
        {
            return [];
        }
    }

    public function generate($meccanografico)
    {
        return $this->valutazioni($meccanografico);
    }
}
