<?php
/**
 * Flusso: Assenze
 * Codice: RA
 * 
 * 
 */
class SiisAssenze extends Siis 
{

    protected $db;
    protected $pdf;
    public $flusso;
    public $versione;

    function __construct($flusso, $database) 
    {
        $this->database = $database;        

        parent::__construct($this->flusso, $this->database);

        $pdf = new PDFClass();
        $this->pdf = $pdf;
        
        $this->flusso = "00RA";
        $this->versione = "20180621";
    }

    function decode($field) 
    {
        if (!is_array($field) && strlen($field) > 0) 
        {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        }
        else
        {
            return $field;
        }
    }

    public function normalize_string($string) 
    {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico() 
    {
        $query = "SELECT valore
                    FROM parametri 
                    WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        
        return $this->db->query($query);
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     * $filename riporta il prefisso dei tre file dell'anagrafe nazionale
     *
     * @return int
     */
    public function createZip($fileName, $codMecc, $timestamp = null) 
    {
        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;

        exec("rm /var/www-source/mastercom/tmp_siis/assenze/ASSENZE_{$codMecc}_{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/assenze/{$fileName}*.TXT");

        foreach ($glob as $file) 
        {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/assenze/ASSENZE_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE) 
            {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/assenze/*.TXT");
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $cod
     */
    public function mergeFile($cod) 
    {
        $annoScolastico = $this->getAnnoScolastico();
        $timestamp = date('YmdHi');
        $timestamp2 = date('Ymd');
        $codMecc = explode('-', $cod)[1];

        $path_files = "/var/www-source/mastercom/tmp_siis/assenze/";

        // creo i file finali
        $codici_scuola = $this->getCodiciScuola($cod);
        $prefisso_file = $codMecc .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolastico['valore'])[0] .
                        $this->flusso .
                        "MAST" .
                        $this->versione .
                        $timestamp;

        $glob = glob($path_files . "ASSENZE_{$codMecc}_{$timestamp2}*.zip");

        // recupero i due file: quello creato in locale e quello creato sull'altro server
        foreach ($glob as $key => $file) 
        {
            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = $path_files . $basename;

            if ($zip->open($nomeZip) == TRUE) 
            {
                exec("mkdir {$path_files}tmp/{$key}/");

                $zip->extractTo("{$path_files}tmp/{$key}/");
                $zip->close();

                $glob_zip = glob("{$path_files}tmp/{$key}/*");

                foreach ($glob_zip as $file_singolo)
                {
                    if (strpos($file_singolo, "001TB_ALU_ASSENZE.TXT") !== false)
                    {
                        // scuole (sovrascrivo in quanto la scuola è una sola)
                        $file_scuole = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}001TB_ALU_ASSENZE.TXT", print_r($file_scuole, true));
                    }
                }

                exec("rm {$path_files}tmp/{$key}/*");
            }
            else
            {
                echo 'Merge files failed';
            }
        }

        // creo lo zip finale
        $this->createZip($prefisso_file, $codMecc, $timestamp);

        // creo il .dat
        file_put_contents("/var/www-source/mastercom/tmp_siis/assenze/##" . $prefisso_file . ".dat", "");

        $fileZip = glob($path_files . 'ASSENZE_' . $codMecc . '_' . $timestamp . '.zip');
        $zip = new ZipArchive;

        foreach ($fileZip as $zipFile) 
        {
            if ($zip->open($zipFile) === TRUE) 
            {
                $zip->addFile('/var/www-source/mastercom/tmp_siis/assenze/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                        or die ("ERROR: Could not add file: {$prefisso_file}");
                $zip->close();
            }
        }
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico) 
    {
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        // Assenze
        $assenze = $this->getAssenze($meccanografico, 'verify');
        if (!empty($assenze['errors'])) 
        {
            foreach ($assenze['errors']['codice_sidi'] as $value) 
            {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Assenze',
                    'valore' => $value
                ];
            }
        }

        if (!empty($result[$meccanografico]['errors_stop'])) 
        {
            return $result[$meccanografico]['errors_stop'];
        }
        else
        {
            return [];
        }
    }
    
    public function generate($meccanografico) 
    {
        return $this->assenze($meccanografico);
    }

    /**
     * Restituisce i dati deglle assenze degli alunni in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getAssenze($meccanografico, $check = null) 
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = [];
        $datiAssenze = [];

        $annoScolastico = $this->getAnnoScolastico();

        $mecc = explode('-', $meccanografico);

        // Estraggo tutti gli studenti
        $query = "SELECT DISTINCT ON (id_studente)
                        id_studente,
                        esito_corrente_calcolato,
                        codice_alunno_ministeriale,
                        codice_fiscale

                    FROM studenti_completi
                    WHERE codice_ministeriale != 'AA'
                        AND classificazione_indirizzi NOT IN ('SE')
                        AND esito_corrente_calcolato not like 'Trasferit%'
                        AND esito_corrente_calcolato not like 'Ritirato%'
                        AND id_codice_ministeriale NOT IN (94)
                        AND descrizione_indirizzi not ilike '%RITIR%'
                        AND descrizione_indirizzi not ilike '%TRASFE%'
                        AND descrizione_indirizzi not ilike '%PREISCR%'
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                        AND classe != '-1'";

        $arrAlunno = $this->db->query($query, true);

        // Ciclo gli studenti e creo l'array delle assenze
        foreach ($arrAlunno as $alunno)
        {
            $dati_studente[$alunno['codice_alunno_ministeriale']] = $alunno;
            
            $id_studente = $alunno['id_studente'];

            // Estraggo le assenze
            $query_assenze = "SELECT id_studente, 
                                    EXTRACT (year FROM to_timestamp(data)) AS anno, 
                                    EXTRACT (month FROM to_timestamp(data)) AS mese, 
                                    tipo_assenza, 
                                    count(*) as totale
                                FROM assenze
                                WHERE data <= " . time() ."
                                    AND id_studente = {$id_studente}
                                GROUP BY 
                                    id_studente, 
                                    anno,
                                    mese,
                                    tipo_assenza
                                ORDER BY id_studente,
                                    anno,
                                    mese, 
                                    tipo_assenza
                                ";
            
            $arrAssenze = $this->db->query($query_assenze, true);

            foreach($arrAssenze as $assenza)
            {
                switch ($assenza['tipo_assenza'])
                {
                    // assenze
                    case 1:
                        $gruppo_assenze = 'A';
                        break;
                    
                    // entrate + assenza mattino
                    case 2:
                    case 6:
                    case 8:
                    case 12:
                        $gruppo_assenze = 'R';
                        break;

                    // uscite +  assenza pomeriggio
                    case 3:
                    case 7:
                    case 9:
                    case 18:
                        $gruppo_assenze = 'U';
                        break;

                    default:
                        $gruppo_assenze = 'X';
                        break;
                }
                
                $valore_anno_scolastico = explode('/', $annoScolastico['valore'])[0] . substr(explode('/', $annoScolastico['valore'])[1], 2, 2); 
                $chiave_assenza = $alunno['codice_alunno_ministeriale'] . "-" . $assenza['anno'] . $assenza['mese'] . $gruppo_assenze;
                
                $datiAssenze[$chiave_assenza]['codice_ministeriale_scuola'] = $mecc[1];
                $datiAssenze[$chiave_assenza]['anno_scolastico'] = $valore_anno_scolastico;
                $datiAssenze[$chiave_assenza]['codice_alunno_sidi'] = $alunno['codice_alunno_ministeriale'];
                $datiAssenze[$chiave_assenza]['mese'] = $this->formatId($assenza['mese'], 2);
                $datiAssenze[$chiave_assenza]['tipo_rilevazione'] = $gruppo_assenze;
                $datiAssenze[$chiave_assenza]['numero_giorni'] += $assenza['totale'];
            }
        }

        // verifico i dati
        if ($check == 'verify') 
        {
            if (is_array($datiAssenze)) 
            {
                foreach ($datiAssenze as $id => $assenze) 
                    {
                    $checkAssenza = $this->checkDatiAssenze($dati_studente[$assenze['codice_alunno_sidi']], $assenze);

                    if (!empty($checkAssenza['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkAssenza['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) 
        {
            return $errors;
        }
        else
        {
            return $datiAssenze;
        }
	//}}} </editor-fold>
    }

    /**
     * Verifica se l'assenza ha i dati corretti
     *
     * @param array $studente
     * @param array $assenze
     * @return string
     */
    public function checkDatiAssenze($studente, $assenze)
    {
        $errors = ['errors' => ''];

        if ($assenze['codice_ministeriale_scuola'] == "") 
        {
            $errors['errors'] = "Errore: codice alunno SIDI mancante per lo studente {$studente['cognome_alunno']} {$studente['nome']}.";
        }

        // SIDI vuole come anno scolastico anno di inizio a 4 cifre, anno di fine a 2 cifre (es. 201718)
        if (strlen($assenze['anno_scolastico']) != 6 || !is_numeric($assenze['anno_scolastico'])) {
            $errors['errors'] = "Errore: anno scolastico errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }
    
        if ($assenze['codice_alunno_sidi'] == '' || !is_numeric($assenze['codice_alunno_sidi'])) {
            $errors['errors'] = "Errore: codice SIDI errato o assente per lo studente {$studente['cognome_alunno']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}"
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }
        
        if (strlen($assenze['mese']) != 2 || !is_numeric($assenze['mese'])) {
            $errors['errors'] = "Errore: mese errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }        
        
        if ($assenze['tipo_rilevazione'] == '' || strlen($assenze['tipo_rilevazione']) != 1) {
            $errors['errors'] = "Errore: codice tipo rilevazione errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }        
        if (!is_numeric($assenze['numero_giorni'])) {
            $errors['errors'] = "Errore: totale mensile errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }
        
        return $errors;
    }

    /**
     * Restituisce l'ID ed il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico)
    {
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola, codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4)
    {
        if (!isset($id) || trim($id) === '') 
        {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++) 
        {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    }

    private function assenze($meccanografico) {

        ini_set('memory_limit', '4096M');

        $datiFinali = $dati_finali_assenze = [];


        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];

        $timestamp = date('YmdHi');
        $mecc = explode('-', $meccanografico);
        $annoScolastico = $this->getAnnoScolastico();
        $codici_scuola = $this->getCodiciScuola($meccanografico);

        $datiAssenze = $this->getAssenze($meccanografico);
        foreach ($datiAssenze as $id_assenza => $assenze)
        {
            $dati_finali_assenze[$id_assenza] = implode("|", $assenze) . "|\n";
        }
        
        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolastico['valore'])[0] .
                        $this->flusso .
                        "MAST" .
                        $this->versione .
                        $timestamp;

        $datiFinali = array_values($dati_finali_assenze);

        if (empty($result[$meccanografico]['errors_stop']))
        {
            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/assenze/*" . $this->flusso . "MAST". $this->versione . "*");

            // Creo il file Assenze
            $filename_assenze = $prefisso_file . '001TB_ALU_ASSENZE.TXT';
            foreach ($datiFinali as $riga_assenza)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/assenze/" . $filename_assenze, print_r($riga_assenza, true), FILE_APPEND);
            }

            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/assenze/ASSENZE_' . explode('-',$meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/assenze/##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile) {
                if ($zip->open($zipFile) === TRUE) {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/assenze/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                            or die ("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/assenze/##*" . $this->flusso . "MAST". $this->versione . "*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));
        file_put_contents('/tmp/datiFinali', print_r($datiFinali, true));

        return $result[$meccanografico]['errors_stop'];
    }
}
