<?php

class SiisCreditiScolastici extends Siis {

    protected $db;
    protected $pdf;

    function __construct($flusso, $database) {
        $this->flusso = $flusso;
        $this->database = $database;

        parent::__construct($this->flusso, $this->database);

        $pdf = new PDFClass();
        $this->pdf = $pdf;
    }

    function decode($field) {
        if (!is_array($field) && strlen($field) > 0) {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        } else {
            return $field;
        }
    }

    function encode($field) {
        //{{{ <editor-fold defaultstate="collapsed">
        // chr(26) = CTRL-Z
        $field = str_replace(
                ["\\", "\'", "'", chr(26), '"'], ['&#92;', '&#039;', '&#039;', " ", "&quot;"], $field
        );

        //$field = mb_convert_encoding($field, "HTML-ENTITIES", "UTF-8, ISO-8859-1, ISO-8859-5");
        $field = mb_convert_encoding($field, "HTML-ENTITIES", mb_detect_encoding($field, "UTF-8, ISO-8859-1, ISO-8859-5, ISO-8859-15", true));

        $result = preg_replace('~^(&([#a-zA-Z0-9]);)~', htmlentities('${1}', ENT_QUOTES), $field);

        return $result;
        //}}} </editor-fold>
    }

    public function normalize_string($string) {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico() {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     * @return int
     */
    public function createZip($fileName, $codMecc) {
        $timestamp2 = date('Ymd');
        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/CREDITI_{$codMecc}_{$timestamp2}*.zip");

        $glob = glob("/var/www-source/mastercom/tmp_siis/crediti_scolastici/{$fileName}.TXT");
        $timestamp = date('YmdHi');

        foreach ($glob as $file) {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/crediti_scolastici/CREDITI_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE) {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/*ESCSMAST20120404001TB_ALU_REG_CRE*.TXT");
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $codMecc
     */
    public function mergeFile($cod) {
        $annoScolatico = $this->getAnnoScolastico();
        $timestamp = date('YmdHi');
        $timestamp2 = date('Ymd');
        $codMecc = explode('-', $cod)[1];

        $glob = glob("/var/www-source/mastercom/tmp_siis/crediti_scolastici/CREDITI_{$codMecc}_{$timestamp2}*.zip");

        foreach ($glob as $key => $file) {
            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/crediti_scolastici/{$basename}";

            if ($zip->open($nomeZip) == TRUE) {
                if ($key == 1) {
                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/crediti_scolastici/');
                    $zip->close();

                    $txt1 = glob("/var/www-source/mastercom/tmp_siis/crediti_scolastici/*{$codMecc}*.TXT");
                    file_put_contents('/tmp/txt1', print_r($txt1, true));
                    foreach ($txt1 as $value) {
                        $basename1 = basename($value);
                        exec("mv /var/www-source/mastercom/tmp_siis/crediti_scolastici/{$basename1} /var/www-source/mastercom/tmp_siis/crediti_scolastici/{$basename1}_1.TXT");
                    }
                } else {
                    exec("mkdir /var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/");

                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/');
                    $zip->close();
                }
            } else {
                echo 'Merge files failed';
            }
        }

        $txt = glob("/var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/*{$codMecc}*.TXT");

        foreach ($txt as $value) {
            $basename = basename($value);
            exec("mv /var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/{$basename} /var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/{$basename}_2.TXT");
            exec("mv /var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/{$basename}_2.TXT /var/www-source/mastercom/tmp_siis/crediti_scolastici/");
        }

        $txt2 = glob("/var/www-source/mastercom/tmp_siis/crediti_scolastici/*{$codMecc}*.TXT");

        $scuola = $this->getScuola($cod);

        if (array_key_exists(0, $scuola)) {
            foreach ($scuola as $value) {
                $codice_prenotazione = $value['codice_prenotazione'];
                $idScuola = $this->formatId($value['id_scuola']);
            }
        } else {
            $codice_prenotazione = $scuola['codice_prenotazione'];
            $idScuola = $this->formatId($scuola['id_scuola']);
        }

        $finalName = $codMecc.$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESCS'.'MAST'.'20120404'.$timestamp.'001TB_ALU_REG_CRE';

        foreach ($txt2 as $value) {
            $file = file_get_contents($value);
            file_put_contents("/var/www-source/mastercom/tmp_siis/crediti_scolastici/{$finalName}.TXT", print_r($file, true), FILE_APPEND);
        }

        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/*{$codMecc}*.TXT_*.TXT");
        exec("mv /var/www-source/mastercom/tmp_siis/crediti_scolastici/CREDITI_{$codMecc}_*.zip /var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/");

        $this->createZip($finalName, $codMecc);

        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/##*ESCSMAST20120404*.dat");
        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/##*ESCSMAST20120404*.dat");
        $datName = '##'.$codMecc.$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESCS'.'MAST'.'20120404'.$timestamp;
        file_put_contents('/var/www-source/mastercom/tmp_siis/crediti_scolastici/'.$datName.'.dat', print_r('', true));

        $fileZip = glob('/var/www-source/mastercom/tmp_siis/crediti_scolastici/CREDITI_'.$codMecc.'_'.$timestamp.'.zip');
        $zip = new ZipArchive;

        foreach ($fileZip as $zipFile) {
            if ($zip->open($zipFile) === TRUE) {
                $zip->addFile('/var/www-source/mastercom/tmp_siis/crediti_scolastici/'. $datName. '.dat', $datName.'.dat')  or die ("ERROR: Could not add file: {$datName}");
                $zip->close();
            }
        }

        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/##*ESCSMAST20120404*.dat");
        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/*.TXT");
        exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/tmp/*.zip");
    }

    /**
     * Restituisce i dati della scuola in questione
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico) {
        $errors = ['errors' => ''];
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                            scuole.id_scuola,
                            scuole.descrizione,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            scuole.codice_prenotazione,
                            sedi.id_sede,
                            sedi.descrizione as sedi_descrizione
                        FROM scuole, sedi, indirizzi
                        WHERE scuole.id_scuola = sedi.id_scuola::int
                            AND sedi.id_sede = indirizzi.id_sede::bigint
                            AND indirizzi.id_codice_ministeriale NOT IN (
                                    SELECT id_indirizzo FROM indirizzi_ministeriali
                                    WHERE classificazione IN ('AA', 'EE')
                                )
                            AND indirizzi.tipo_indirizzo NOT IN ('6','7')
                            AND indirizzi.flag_canc = 0
                            AND scuole.flag_canc = 0
                            AND sedi.flag_canc = 0
                            AND codice_meccanografico = '{$mecc[0]}'
                            AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $datiScuola =  $this->db->query($query);

        if (array_key_exists(0, $datiScuola)) {
            foreach ($datiScuola as $value) {
                if ($value['codice_prenotazione'] == '') {
                    $errors['errors']['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$value['codice_meccanografico']}-{$value['codice_meccanografico_secondario']}";

                    if (!empty($errors['errors'])) {
                        $datiScuola['errors'] = $errors['errors'];
                    }
                }
            }
        } else {
            if ($datiScuola['codice_prenotazione'] == '') {
                $errors['errors']['codice_prenotazione'] = "Errore: manca il codice prenotazione per la scuola {$datiScuola['codice_meccanografico']}-{$datiScuola['codice_meccanografico_secondario']}";

                if (!empty($errors['errors'])) {
                    $datiScuola['errors'] = $errors['errors'];
                }
            }
        }

        return $datiScuola;
    }

    /**
     * Verifica se lo studente ha il codice SIDI settato
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student) {
        $errors = ['errors' => ''];

        if ($student['codice_alunno_ministeriale'] == '') {
            $errors['errors'] = "Errore: manca il codice SIDI per {$student['cognome']} {$student['nome']} classe {$student['classe']}{$student['sezione']}. "
            . "<br> Per scaricare il codice SIDI andare in Setup C06 - ESTRAZIONE ALUNNI SOGEI, inserire le credenziali, selezionare l'istituto e premere su Procedere";
        }

        return $errors;
    }

    /**
     * Estrae l'elenco degli studenti per la scuola in questione con i seguenti
     * filtri:
     *
     * codice_ministeriale <> 'FAKE' (Indirizzo non FAKE)
     * classi 1,2,3,4 per indirizzi Superiori tranne Classico
     * classi 1,2,4,5 per indirizzo Superiore Classico
     * classi 1,2,3 per indirizzo Medie
     * esito <> Trasferito e Ritiraro (recuperato dalla riga di curriculum)
     *
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null) {
        $errors = ['errors' => ''];

        $annoScolatico = $this->getAnnoScolastico();
        $asI = explode('/', $annoScolatico['valore'])[0];
        $asF = explode('/', $annoScolatico['valore'])[1];

        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                            sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            sc.sezione_sidi,
                            sc.descrizione_indirizzi,
                            codice_ministeriale,
                            tipo_indirizzo,
                            giudizio_ammissione_quinta,
                            ammesso_esame_quinta,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            handicap,
                            grado_handicap,
                            tipo_handicap,
                            esito_corrente_calcolato,
                            codice_meccanografico,
                            codice_meccanografico_secondario
                        FROM studenti_completi sc, storia_studenti ss
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND sc.ordinamento = '0'
                            AND sc.classe = '4'
                            AND tipo_indirizzo = '5'
                            AND sc.classificazione_indirizzi IN ('PR','EI','MM','PQ','SE')
                            AND sc.id_studente = ss.id_studente
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'

                UNION

                SELECT DISTINCT
                            sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            sc.sezione_sidi,
                            sc.descrizione_indirizzi,
                            codice_ministeriale,
                            tipo_indirizzo,
                            giudizio_ammissione_quinta,
                            ammesso_esame_quinta,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            handicap,
                            grado_handicap,
                            tipo_handicap,
                            esito_corrente_calcolato,
                            codice_meccanografico,
                            codice_meccanografico_secondario
                        FROM studenti_completi sc, storia_studenti ss
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND sc.ordinamento = '0'
                            AND sc.classe = '3'
                            AND tipo_indirizzo = '1'
                            AND sc.classificazione_indirizzi IN ('PR','EI','MM','PQ','SE')
                            AND sc.id_studente = ss.id_studente
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'

                UNION

                SELECT DISTINCT
                            sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            sc.sezione_sidi,
                            sc.descrizione_indirizzi,
                            codice_ministeriale,
                            tipo_indirizzo,
                            giudizio_ammissione_quinta,
                            ammesso_esame_quinta,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            handicap,
                            grado_handicap,
                            tipo_handicap,
                            esito_corrente_calcolato,
                            codice_meccanografico,
                            codice_meccanografico_secondario
                        FROM studenti_completi sc, storia_studenti ss
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND sc.ordinamento = '0'
                            AND sc.classe = '5'
                            AND tipo_indirizzo IN ('0','2','3')
                            AND sc.classificazione_indirizzi IN ('PR','EI','MM','PQ','SE')
                            AND sc.id_studente = ss.id_studente
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'
                        ORDER BY codice_ministeriale, classe, sezione, cognome, nome
                        ";

        $arrAlunno = $this->db->query($query);

        if ($check) {
            if (is_array($arrAlunno)) {
                foreach ($arrAlunno as $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            } else {
                $errors['errors']['nessun_dato_presente'][] = "Non sono presenti dati da elaborare per la scuola {$mecc[0]}-{$mecc[1]}";
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $arrAlunno;
        }
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4) {
        if (!isset($id) || trim($id) === '') {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++) {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    }

    private function crediti($meccanografico) {
        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];
        $datiFlusso = [];

        $annoScolatico = $this->getAnnoScolastico();
        $asI = explode('/', $annoScolatico['valore'])[0];
        $asF = explode('/', $annoScolatico['valore'])[1];
        if (intval($asI == 2019) || intval($asI == 2020))
        {
            $crediti_max_possibili = 60;
        }
        else
        {
            $crediti_max_possibili = 40;
        }
        $scuola = $this->getScuola($meccanografico);

        $datiStudente = $this->getStudenti($meccanografico);

        foreach ($datiStudente as $studente)
        {
            $studente['ammesso_esame_quinta'] = substr($studente['ammesso_esame_quinta'], 0, -1);
            //$giudizio_ammissione = $this->normalize_string($studente['giudizio_ammissione_quinta']);
            $giudizio_ammissione = preg_replace( "/\r|\n/", "", $studente['giudizio_ammissione_quinta']);

            if ($studente['ammesso_esame_quinta'] == 'S')
            {
                if ($studente['classe'] == 5 &&
                    ($studente['crediti_terza'] == 0  || $studente['crediti_quarta'] == 0 || $studente['crediti_quinta'] == 0)
                )
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc' => 'Crediti Mancanti',
                        'valore' => $studente['cognome'].' '.$studente['nome']. ' '. $studente['classe'].' '.$studente['sezione'].' '.$studente['descrizione_indirizzi']
                    ];
                }

                $crediti_terza = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'];
                $crediti_quarta = $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'];

                if (intval($asI == 2019) || intval($asI == 2020))
                {
                    if (intval($studente['crediti_quinta']) < 9 || intval($studente['crediti_quinta']) > 22)
                    {
                        $crediti_quinta = '';
                    }
                    else
                    {
                        $crediti_quinta = $studente['crediti_quinta'];
                    }
                }
                else
                {
                    if (intval($studente['crediti_quinta']) < 7 || intval($studente['crediti_quinta']) > 15)
                    {
                        $crediti_quinta = '';
                    }
                    else
                    {
                        $crediti_quinta = $studente['crediti_quinta'];
                    }
                }


                $totale_crediti = $crediti_terza + $crediti_quarta + $crediti_quinta;

                if ($totale_crediti == 0 || $totale_crediti == '')
                {
                    $totale_crediti = '';
                }
                else
                {
                    if ($totale_crediti == $crediti_max_possibili)
                    {
                        $possibile_lode = 'S';
                    }
                    else
                    {
                        $possibile_lode = 'N';
                    }
                }

                if (($studente['grado_handicap'] != '0' && $studente['grado_handicap'] != '') || $studente['tipo_handicap'] != '')
                {
                    $presenza_disabilita = 'S';

                    if ($studente['tipo_handicap'] == 'DSA')
                    {
                        $presenza_dsa = 'S';
                    }
                    else
                    {
                        $presenza_dsa = 'N';
                    }
                }
                else
                {
                    $presenza_disabilita = 'N';
                    $presenza_dsa = 'N';
                }

                if (($studente['grado_handicap'] != '0' && $studente['grado_handicap'] != '') || $studente['tipo_handicap'] == 'DSA')
                {
                    $presenza_disabilita = 'N';

                    if ($studente['tipo_handicap'] == 'DSA')
                    {
                        $presenza_dsa = 'S';
                    }
                    else
                    {
                        $presenza_dsa = 'N';
                    }
                }
            }
            else
            {
                $crediti_terza = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'];
                $crediti_quarta = $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'];
                $crediti_quinta = $studente['crediti_quinta'];
                //Indicazione da sidi il 12/06/2019
                //nel caso di non ammissione il campo Credito Totale non deve essere valorizzato.
                $totale_crediti = '';

                if ($totale_crediti == $crediti_max_possibili)
                {
                    $possibile_lode = 'S';
                }
                else
                {
                    $possibile_lode = 'N';
                }

                if (($studente['grado_handicap'] != '0' && $studente['grado_handicap'] != '') || $studente['tipo_handicap'] != '')
                {
                    $presenza_disabilita = 'S';

                    if ($studente['tipo_handicap'] == 'DSA')
                    {
                        $presenza_dsa = 'S';
                    }
                    else
                    {
                        $presenza_dsa = 'N';
                    }
                }
                else
                {
                    $presenza_disabilita = 'N';
                    $presenza_dsa = 'N';
                }

                if (($studente['grado_handicap'] != '0' && $studente['grado_handicap'] != '') || $studente['tipo_handicap'] == 'DSA')
                {
                    $presenza_disabilita = 'N';

                    if ($studente['tipo_handicap'] == 'DSA')
                    {
                        $presenza_dsa = 'S';
                    }
                    else
                    {
                        $presenza_dsa = 'N';
                    }
                }
            }

            $chiaveDatiFlusso = $studente['classe'] . ' ' . $studente['sezione'] . ' ' . $studente['cognome'] . ' ' . $studente['nome'] . ' - ' . $studente['codice_fiscale'];

            $datiFlusso[$chiaveDatiFlusso][] = [
                $studente['codice_alunno_ministeriale'] . "|",
                $studente['ammesso_esame_quinta'] . "|",
                $crediti_terza . "|",
                $crediti_quarta . "|",
                $crediti_quinta . "|",
                $possibile_lode . "|",
                $presenza_disabilita . "|",
                $presenza_dsa . "|",
                $totale_crediti . "|",
                $this->decode(trim($giudizio_ammissione)) . "|"
            ];
        }

        $datiFinali = array_values($datiFlusso);

        if (empty($result[$meccanografico]['errors_stop']))
        {
            $result[$meccanografico]['correct'] = 'File elaborato correttamente';

            $timestamp = date('YmdHi');

            if (array_key_exists(0, $scuola))
            {
                foreach ($scuola as $value)
                {
                    $codice_prenotazione = $value['codice_prenotazione'];
                    $idScuola = $this->formatId($value['id_scuola']);
                }
            }
            else
            {
                $codice_prenotazione = $scuola['codice_prenotazione'];
                $idScuola = $this->formatId($scuola['id_scuola']);
            }

            $fileName = explode('-',$meccanografico)[1].$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESCS'.'MAST'.'20120404'.$timestamp.'001TB_ALU_REG_CRE';

            foreach ($datiFinali as $righe)
            {
                foreach ($righe as $riga)
                {
                    foreach ($riga as $key => $value)
                    {
                        if ($key==9) {
                            $value.="\n";
                        }

                        file_put_contents('/var/www-source/mastercom/tmp_siis/crediti_scolastici'."/{$fileName}.TXT", print_r($value, true), FILE_APPEND);
                    }
                }
            }

            $this->createZip($fileName, explode('-',$meccanografico)[1]);

//            $timestamp = date('Ymd').time();

            exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/##*ESCSMAST20120404*.dat");
            $datName = '##'.explode('-',$meccanografico)[1].$codice_prenotazione.$idScuola.explode('/', $annoScolatico['valore'])[0].'ESCS'.'MAST'.'20120404'.$timestamp;
            file_put_contents('/var/www-source/mastercom/tmp_siis/crediti_scolastici/'.$datName.'.dat', print_r('', true));

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/crediti_scolastici/CREDITI_'.explode('-',$meccanografico)[1].'_'.$timestamp.'.zip');
            $zip = new ZipArchive;

            foreach ($fileZip as $zipFile) {
                if ($zip->open($zipFile) === TRUE) {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/crediti_scolastici/'. $datName. '.dat', $datName.'.dat')  or die ("ERROR: Could not add file: {$datName}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/##*ESCSMAST20120404*.dat");
        }

        if (!empty($result[$meccanografico]['errors_stop'])) {
//            $result[$meccanografico]['correct'] = 'File elaborato correttamente';

//            $idScuola = $this->formatId($scuola['id_scuola']);
//            $fileName = explode('-',$meccanografico)[1].$scuola['codice_prenotazione'].$idScuola.explode('/', $annoScolatico['valore'])[0].'ESCS'.'MAST'.'20120404'.'001TB_ALU_REG_CRE';
//
//            foreach ($datiFinali as $righe) {
//                foreach ($righe as $riga) {
//                    foreach ($riga as $key => $value) {
//                        if ($key==13) {
//                            $value.="\n";
//                        }
//
//                        file_put_contents('/var/www-source/mastercom/tmp_siis/crediti_scolastici'."/{$fileName}.txt", print_r($value, true), FILE_APPEND);
//                    }
//                }
//            }
//
//            $this->createZip($fileName, explode('-',$meccanografico)[1]);
        }

        file_put_contents('/tmp/result', print_r($result, true));
        file_put_contents('/tmp/datiFinali', print_r($datiFinali, true));

        return $result[$meccanografico]['errors_stop'];
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico) {
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        $scuola = $this->getScuola($meccanografico);

        if (!empty($scuola['errors'])) {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Codice di Prenotazione',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
        }

        $datiStudente = $this->getStudenti($meccanografico, 'check');

        if (!empty($datiStudente['errors'])) {
            if (!empty($datiStudente['errors']['nessun_dato_presente'])) {
                foreach ($datiStudente['errors']['nessun_dato_presente'] as $value) {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Nessun dato presente',
                        'valore' => $value
                    ];
                }

                return $result[$meccanografico]['errors_stop'];
            }

            if (!empty($datiStudente['errors']['codice_sidi'])) {
                foreach ($datiStudente['errors']['codice_sidi'] as $value) {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Codice Sidi',
                        'valore' => $value
                    ];
                }
            }
        }

        if (!empty($result[$meccanografico]['errors_stop'])) {
            return $result[$meccanografico]['errors_stop'];
        } else {
            return [];
        }
    }

    public function generate($meccanografico) {
        return $this->crediti($meccanografico);
    }
}
