<?php

class SiisAnagrafeNazionale extends Siis {

    protected $db;
    protected $pdf;

    function __construct() {
        $db = new MT\Mastercom\Db();
        $this->db = $db;

        $pdf = new PDFClass();
        $this->pdf = $pdf;
    }

    function decode($field) {
        if (!is_array($field) && strlen($field) > 0) {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        } else {
            return $field;
        }
    }

    public function normalize_string($string) {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico() {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     * $filename riporta il prefisso dei tre file dell'anagrafe nazionale
     *
     * @return int
     */
    public function createZip($fileName, $codMecc, $timestamp = null) {

        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;

        exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale/NUOVOAVVIOSCUOLA_{$codMecc}_{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/{$fileName}*.TXT");

        foreach ($glob as $file) {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/NUOVOAVVIOSCUOLA_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE) {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale/*.TXT");
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $cod
     */
    public function mergeFile($cod) {

        $annoScolatico = $this->getAnnoScolastico();
        $timestamp = date('YmdHi');
        $timestamp2 = date('Ymd');
        $codMecc = explode('-', $cod)[1];

        $path_files = "/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/";

        // creo i file finali
        $codici_scuola = $this->getCodiciScuola($cod);
        $prefisso_file = $codMecc .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolatico['valore'])[0] .
                        "00NF" .
                        "MAST" .
                        "20160930" .
                        $timestamp;

        $glob = glob($path_files . "NUOVOAVVIOSCUOLA_{$codMecc}_{$timestamp2}*.zip");

        // recupero i due file: quello creato in locale e quello creato sull'altro server
        foreach ($glob as $key => $file) {

            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = $path_files . $basename;

            if ($zip->open($nomeZip) == TRUE) {

                exec("mkdir {$path_files}tmp/{$key}/");

                $zip->extractTo("{$path_files}tmp/{$key}/");
                $zip->close();

                $glob_zip = glob("{$path_files}tmp/{$key}/*");

                foreach ($glob_zip as $file_singolo)
                {
                    if (strpos($file_singolo, "001TB_USR_USER.TXT") !== false)
                    {
                        // scuole (sovrascrivo in quanto la scuola è una sola)
                        $file_scuole = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}001TB_USR_USER.TXT", print_r($file_scuole, true));
                    }

                    if (strpos($file_singolo, "002TB_ALU_ALUNNI.TXT") !== false)
                    {
                        // studenti
                        $file_studenti = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}002TB_ALU_ALUNNI.TXT", print_r($file_studenti, true), FILE_APPEND);
                    }

                    if (strpos($file_singolo, "003TB_ALU_SIT.TXT") !== false)
                    {
                        // curriculum
                        $file_curriculum = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}003TB_ALU_SIT.TXT", print_r($file_curriculum, true), FILE_APPEND);
                    }

                    if (strpos($file_singolo, "004TB_ALU_FRQEST.TXT") !== false)
                    {
                        // frequentanti estero
                        $file_frequentanti_estero = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}004TB_ALU_FRQEST.TXT", print_r($file_frequentanti_estero, true), FILE_APPEND);
                }
                }
                exec("rm {$path_files}tmp/{$key}/*");

            }
            else
            {
                echo 'Merge files failed';
            }
        }

        // creo lo zip finale
        $this->createZip($prefisso_file, $codMecc, $timestamp);

        // creo il .dat
        file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/##" . $prefisso_file . ".dat", "");

        $fileZip = glob($path_files . 'NUOVOAVVIOSCUOLA_' . $codMecc . '_' . $timestamp . '.zip');
        $zip = new ZipArchive;

        foreach ($fileZip as $zipFile) {
            if ($zip->open($zipFile) === TRUE) {
                $zip->addFile('/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                        or die ("ERROR: Could not add file: {$prefisso_file}");
                $zip->close();
            }
        }
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico) {
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        // Scuola
        $scuola = $this->getScuola($meccanografico, 'verify');
        if (!empty($scuola['errors'])) {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Scuola',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
        }

        // Alunno
        $datiStudente = $this->getStudenti($meccanografico, 'verify');
        if (!empty($datiStudente['errors'])) {
            foreach ($datiStudente['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Studenti',
                    'valore' => $value
                ];
            }
        }

        // Curriculum
        $datiCurriculum = $this->getCurriculum($meccanografico, 'verify');
        if (!empty($datiCurriculum['errors'])) {
            foreach ($datiCurriculum['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Curriculum',
                    'valore' => $value
                ];
            }
        }

        // Curriculum
        $datiFrequentantiEstero = $this->getFrequentantiEstero($meccanografico, 'verify');
        if (!empty($datiFrequentantiEstero['errors'])) {
            foreach ($datiFrequentantiEstero['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Frequentanti Estero',
                    'valore' => $value
                ];
            }
        }

        if (!empty($result[$meccanografico]['errors_stop'])) {
            return $result[$meccanografico]['errors_stop'];
        } else {
            return [];
        }
    }

    public function generate($meccanografico) {
        return $this->anagrafe_nazionale($meccanografico);
    }

   /**
     * Restituisce i dati della scuola in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = [];
        $datiScuola = [];

        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                        sc.id_scuola,
                        sc.codice_fiscale,
                        sc.codice_meccanografico,
                        sc.codice_meccanografico_secondario,
                        sc.descrizione,
                        sc.codice_fiscale,
                        sc.persona_giuridica,
                        sc.tipo_istituto_abbreviato,
                        sc.sito_scuola,
                        sc.decreto_ministeriale,
                        sc.id_tipo_scuola,
                        sc.istituto_dichiarante,
                        sc.codice_prenotazione,

                        sedi.id_comune,
                        sedi.email1,
                        sedi.email2,
                        sedi.cap,
                        sedi.indirizzo,
                        sedi.id_sede,
                        sedi.descrizione as sedi_descrizione,
                        sedi.cap,
                        sedi.telefono,
                        sedi.telefono2,

                        CASE
                            WHEN indirizzi_ministeriali.classificazione IN ('EE', 'AA', 'MM') THEN indirizzi_ministeriali.classificazione
                            ELSE 'SS'
                        END AS ordine,

                        ts.descrizione as descr_tipo

                    FROM scuole sc,
                        sedi,
                        indirizzi,
                        indirizzi_ministeriali,
                        tipi_scuole ts

                    WHERE sc.id_scuola = sedi.id_scuola::int
                        AND ts.codice = sc.id_tipo_scuola
                        AND sedi.id_sede = indirizzi.id_sede::bigint
                        AND indirizzi.id_codice_ministeriale = indirizzi_ministeriali.id_indirizzo
                        AND indirizzi_ministeriali.classificazione NOT IN ('AA')
                        AND indirizzi.tipo_indirizzo NOT IN ('7')
                        AND indirizzi.flag_canc = 0
                        AND sc.flag_canc = 0
                        AND sedi.flag_canc = 0
                        AND ts.flag_canc = 0
                        AND indirizzi_ministeriali.flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                        LIMIT 1";

        $arrScuola =  $this->db->query($query);

        // verifica dati
        if (!empty($arrScuola))
        {
            if (strlen(str_replace("&#039;" ,"",$arrScuola['tipo_istituto_abbreviato'])) > 16) {
                $errors['codice_prenotazione'][] = "Errore: Il tipo scuola è troppo lungo per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}.<br>La lunghezza massima consentita è di 16 caratteri.";
            }
            if ($arrScuola['codice_prenotazione'] == '') {
                $errors['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
            if ($arrScuola['id_comune'] == '') {
                $errors['codice_prenotazione'][] = "Errore: manca il codice del comune per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
            if (strlen($arrScuola['codice_fiscale']) > 16) {
                $errors['codice_prenotazione'][] = "Errore: codice fiscale errato per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
        }
        else
        {
            $errors['codice_prenotazione'][] = "Dati Scuola mancanti per la scuola {$mecc[0]} - {$mecc[1]}";
        }

        $datiScuola[$arrScuola["id_scuola"]] = [
            'codice_cap'								=>	$arrScuola['cap'],
            'codice_fiscale_scuola'						=>	$arrScuola['codice_fiscale'],
            'codice_meccanografico_forte_scuola'		=>	$arrScuola['codice_meccanografico'],
            'codice_meccanografico_debole_scuola'		=>	$arrScuola['codice_meccanografico_secondario'],
            'codice_comune'								=>	$arrScuola['id_comune'],
            'descrizione_indirizzo_posta_elettronica'	=>	$arrScuola['email1'],
            'secondo_indirizzo_posta_elettronica'		=>	$arrScuola['email2'],
            'riservato_al_gestore_01'					=>	"",
            'riservato_al_gestore_02'					=>	"",
            'riservato_al_gestore_03'					=>	"",
            'riservato_al_gestore_04'					=>	"",
            'riservato_al_gestore_05'					=>	"",
            'riservato_al_gestore_06'					=>	"",
            'identificativo_scuola' 					=>	$arrScuola['codice_prenotazione'] . $this->formatId($arrScuola['id_scuola']),
            'descrizione_indirizzo_scuola'				=>	$arrScuola['indirizzo'],
            'descrizione_nome_scuola'					=>	$arrScuola['nome'],
            'codice_scuola_utente'						=>	$arrScuola['ordine'],
            'flag_persona_giuridica'					=>	$arrScuola['persona_giuridica'],
            'descrizione_sigla_scuola'					=>	str_replace("&#039;" ,"",$arrScuola['tipo_istituto_abbreviato']),
            'descrizione_indirizzo_sito_internet'		=>	$arrScuola['sito_scuola'],
            'numero_telefonico'							=>	$arrScuola['telefono'],
            'secondo_numero_telefonico'					=>	$arrScuola['telefono2'],
            'descrizione_tipo_scuola'					=>	$arrScuola['descr_tipo'],
            'decreto_ministeriale'						=>	$arrScuola['decreto_ministeriale'],
            'tipo'										=>	$arrScuola['id_tipo_scuola'],
            'flag_istituto_dichiarante'					=>	$arrScuola['istituto_dichiarante'],

        ];

        if ($check == 'verify')
        {
            // aggiungo gli eventuali errori
            $datiScuola['errors'] = $errors;
        }

        return $datiScuola;
        //}}} </editor-fold>
    }

    /**
     * Restituisce i dati degli alunni in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = [];
        $datiStudenti = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);

        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT ON (id_studente)
                    id_studente,
                    data_nascita,
                    codice_alunno_ministeriale,
                    flag_cf_fittizio,
                    flag_s2f,
                    cap_residenza,
                    codice_fiscale,
                    cittadinanza,
                    seconda_cittadinanza,
                    codice_comune_nascita,
                    cognome,
                    c.codice_sidi as codice_comune_residenza, -- Da comuni (se esiste)
                    distretto,
                    flag_minore_straniero,
                    codice_gruppo_nomade,
                    indirizzo,
                    matricola,
                    nome,
                    id_scuola,
                    sesso,
                    stato_civile,
                    stato_nascita,
                    citta_nascita_straniera
                FROM studenti_completi sc
                LEFT JOIN comuni c ON sc.codice_comune_residenza = c.id_comune
                WHERE sc.codice_ministeriale != 'AA'
                    AND sc.classificazione_indirizzi NOT IN ('SE')
                    AND sc.esito_corrente_calcolato NOT LIKE 'Trasferit%'
                    AND sc.esito_corrente_calcolato NOT LIKE 'Ritirato%'
                    AND sc.id_codice_ministeriale NOT IN (94)
                    AND sc.descrizione_indirizzi NOT ILIKE '%RITIR%'
                    AND sc.descrizione_indirizzi NOT ILIKE '%TRASFE%'
                    AND sc.descrizione_indirizzi NOT ILIKE '%PREISCR%'
                    AND sc.codice_meccanografico = '{$mecc[0]}'
                    AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                    AND sc.classe != '-1';
                ";

        $arrAlunno = $this->db->query($query, true);

        foreach ($arrAlunno as $alunno)
        {
            $id_studente = $alunno['id_studente'];

            $data_nascita = !in_array($alunno['data_nascita'], ['-1','']) ? date('Y-m-d', $alunno['data_nascita']) : "";

            if ($alunno['seconda_cittadinanza'] == '-1') {
                $alunno['seconda_cittadinanza'] = '';
            }

            $alunno['cittadinanza'] = str_replace("-", "", $alunno['cittadinanza']);
            $alunno['seconda_cittadinanza'] = str_replace("-", "", $alunno['seconda_cittadinanza']);
            $alunno['stato_nascita'] = str_replace("-", "", $alunno['stato_nascita']);
            $alunno['codice_comune_nascita'] = str_replace("-", "", $alunno['codice_comune_nascita']);
            $alunno['citta_nascita_straniera'] = str_replace("-", "", $alunno['citta_nascita_straniera']);

            if ($alunno['stato_nascita'] != '0001') {
                $alunno['codice_comune_nascita'] = "";
            }

            if ($alunno['stato_nascita'] == '0001') {
                $alunno['stato_nascita'] = '200';
            }

            if ($alunno['cittadinanza'] == '0001') {
                $alunno['cittadinanza'] = '200';
            }

            if ($alunno['seconda_cittadinanza'] == '0001') {
                $alunno['seconda_cittadinanza'] = '200';
            }

            if ($alunno['cittadinanza'] == $alunno['seconda_cittadinanza']) {
                $alunno['seconda_cittadinanza'] = '';
            }

            if ($alunno['descrizione_stato_nascita'] == $alunno['citta_nascita_straniera']) {
                $alunno['citta_nascita_straniera'] = '';
            }

            if ($alunno['codice_comune_nascita'] == 'XXXX' || $alunno['codice_comune_nascita'] == '-----') {
                $alunno['codice_comune_nascita'] = '';
            }

            if ($alunno['codice_comune_residenza'] == 'XXXX' || $alunno['codice_comune_residenza'] == '------') {
                $alunno['codice_comune_residenza'] = '';
            }

            if ($alunno['cittadinanza'] != "") {
                $alunno['cittadinanza'] = (int) $alunno['cittadinanza'];
            }

            if ($alunno['seconda_cittadinanza'] != "") {
                $alunno['seconda_cittadinanza'] = (int) $alunno['seconda_cittadinanza'];
            }

            if ($alunno['stato_nascita'] != "") {
                $alunno['stato_nascita'] = (int) $alunno['stato_nascita'];
            }

            $datiStudenti[$id_studente] = [
                "data_aggiornamento_sogei"              => "",
                "data_di_nascita"                       => $data_nascita,
                "codice_alunno_ministeriale"            => $alunno['codice_alunno_ministeriale'],
                "flag_codice_fiscale_fittizio"          => $alunno['flag_cf_fittizio'],
                "riservato_al_gestore_01"               => "",
                "flag_s2f"                              => $alunno['flag_s2f'],
                "anni_frequenza_scuola_materna"         => "",
                "riservato_al_gestore_02"               => "",
                "riservato_al_gestore_03"               => "",
                "riservato_al_gestore_04"               => "",
                "codice_cap_residenza"                  => $alunno['cap_residenza'],
                "codice_fiscale"                        => $alunno['codice_fiscale'],
                "codice_prima_cittadinanza"             => $alunno['cittadinanza'],
                "codice_seconda_cittadinanza"           => $alunno['seconda_cittadinanza'],
                "codice_comune_nascita_alunno"          => $alunno['codice_comune_nascita'],
                "cognome_alunno"                        => $alunno['cognome'],
                "codice_comune_residenza"               => $alunno['codice_comune_residenza'],
                "distretto"                             => $alunno['distretto'],
                "riservato_al_gestore_05"               => "",
                "flag_minore_straniero"                 => $alunno['flag_minore_straniero'],
                "frazione"                              => "",
                "codice_gruppo_nomade"                  => $alunno['codice_gruppo_nomade'],
                "riservato_al_gestore_06"               => "",
                "identificativo_alunno"                 => $codici_scuola['codice_prenotazione'] . $alunno['codice_fiscale'],
                "indirizzo_residenza"                   => $alunno['indirizzo'],
                "riservato_al_gestore_07"               => "",
                "matricola"                             => $alunno['matricola'],
                "riservato_al_gestore_08"               => "",
                "riservato_al_gestore_09"               => "",
                "nome"                                  => $alunno['nome'],
                "riservato_al_gestore_10"               => "",
                "identificativo_scuola"                 => $codici_scuola['codice_prenotazione'] . $this->formatId($alunno['id_scuola']),
                "sesso"                                 => $alunno['sesso'],
                "indicatore_frequenza_scuola_materna"   => "",
                "stato_civile"                          => $alunno['stato_civile'],
                "stato_di_nascita"                      => $alunno['stato_nascita'],
                "codice_stato_sogei"                    => "",
                "riservato_al_gestore_11"               => "",
                "riservato_al_gestore_12"               => "",
                "riservato_al_gestore_13"               => "",
                "luogo_nascita_estero"                  => $alunno['citta_nascita_straniera'],

            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiStudenti)) {
                foreach ($datiStudenti as $id => $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiStudenti;
        }
	//}}} </editor-fold>
    }

    /**
     * Restituisce i dati del curriculum degli alunni
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getCurriculum($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">
        $datiCurriculum = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);

        $mecc = explode('-', $meccanografico);
        $annoScolatico = $this->getAnnoScolastico();

        $query_studenti = "SELECT DISTINCT ON (id_studente)
                                id_studente,
                                cognome,
                                nome,
                                codice_fiscale,
                                materia_sostitutiva_religione,
                                materia_sostitutiva_edfisica,
                                esonero_religione,
                                esonero_ed_fisica,
                                registro,
                                servizio_mensa,
                                stato_privatista,
                                obbligo_formativo,
                                id_quadro_orario_sidi,
                                id_scuola
                            FROM studenti_completi
                            WHERE codice_ministeriale != 'AA'
                                AND ordinamento = '0'
                                AND classificazione_indirizzi NOT IN ('SE')
                                AND id_codice_ministeriale NOT IN (94)
                                AND esito_corrente_calcolato not like 'Trasferit%'
                                AND esito_corrente_calcolato not like 'Ritirato%'
                                AND descrizione_indirizzi not ilike '%RITIR%'
                                AND descrizione_indirizzi not ilike '%TRASFE%'
                                AND descrizione_indirizzi not ilike '%PREISCR%'
                                AND codice_meccanografico = '{$mecc[0]}'
                                AND codice_meccanografico_secondario = '{$mecc[1]}'
                                AND classe != '-1'";

        $arrStudenti = $this->db->query($query_studenti, true);

        foreach ($arrStudenti as $studente)
        {
            $id_studente = $studente['id_studente'];

            // Curriculum Studente da storia studenti
            $query_studente = "SELECT *
                        FROM storia_studenti
                        WHERE id_studente = {$id_studente}
                            AND flag_canc = 0
                            AND anno_scolastico = '{$annoScolatico['valore']}'";

            $arrCurrStudente = $this->db->query($query_studente, true);

            //{{{ <editor-fold defaultstate="collapsed" desc="Iscrizione + classe tradotta">
            foreach ($arrCurrStudente as $idx => $singleCurr) {
                //Classe
                switch ($singleCurr['classe']) {
                    //{{{ <editor-fold defaultstate="collapsed" desc="Conversione classi curriculum">
                    case 6:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '1';
                        break;
                    case 7:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '2';
                        break;
                    case 8:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '3';
                        break;
                    case 9:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '1';
                        break;
                    case 10:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '2';
                        break;
                    case 11:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '3';
                        break;
                    case 12:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '4';
                        break;
                    case 13:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '5';
                        break;
                    case 19:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '1';
                        break;
                    case 20:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '2';
                        break;
                    case 21:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '3';
                        break;
                    case 22:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '4';
                        break;
                    case 23:
                        $arrCurrStudente[$idx]['classe_tradotta'] = '5';
                        break;
                    //}}} </editor-fold>
                }

                //Iscrizione
                if (strtoupper($singleCurr['esito']) == 'ISCRITTO') {
                    $dataIscrizione = date('Y-m-d', $singleCurr['data_riferimento']);
                    break;
                }
            }
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Provenienti da altra scuola">
            $arrCurrStudenteReverse = $arrCurrStudente;
            krsort($arrCurrStudenteReverse);
            $curr_prec = [];
            $data_inizio_frequenza = null;

            foreach ($arrCurrStudenteReverse as $singleCurr) {
                if (!empty($curr_prec)) {
                    if ($curr_prec['esito'] == 'Trasferito' && $singleCurr['esito'] == 'Iscritto') {
                        $data_inizio_frequenza = date('Y-m-d', $singleCurr['data_riferimento']);
                    }
                }

                if ($singleCurr['esito'] == 'Trasferito' && $singleCurr['id_scuola'] != '' && $singleCurr['id_scuola'] != 'XXXX') {
                    $scuola_esterna = true;
                    if ($singleCurr['id_scuola'] == $mecc[1]) {
                        $scuola_esterna = false;
                    }
                    if ($scuola_esterna) {
                        $curr_prec = $singleCurr;
                    }
                }
            }
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Curriculum studente">
            $subquery = "SELECT DISTINCT esito, anno_scolastico
                            FROM storia_studenti
                            WHERE id_studente = {$id_studente}
                                AND flag_canc = 0
                                AND id_scuola IN ('" . $mecc[0] . "', '" . $mecc[1] . "', '')
                                AND upper(esito) = 'ISCRITTO'";

            $query = "SELECT COUNT(*) FROM ($subquery) AS progressivo_curr";

            $dati_curriculum = $this->db->query($query, true);

            $progressivoCurr = str_pad($dati_curriculum[0]['count'], 4, '0', STR_PAD_LEFT);
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Trasferimento o Ritiro">
            $arrUltimaRiga = [];

            if (count($arrCurrStudente) > 0) {
                $subquery = "SELECT DISTINCT esito,
                                    anno_scolastico
                                FROM storia_studenti
                                WHERE id_studente = {$id_studente}
                                    AND flag_canc = 0
                                    AND id_scuola IN ('" . $mecc[0] . "', '" . $mecc[1] . "', '')
                                    AND upper(esito) = 'ISCRITTO'
                                    AND classe IN (" . $arrCurrStudente[0]['classe'] . "," . ($arrCurrStudente[0]['classe'] + 20) . ")";

                $query = "SELECT COUNT(*) FROM ({$subquery}) AS num_iscrizioni_classe";

                $arrNumVolteIscrittoClasse = $this->db->query($query, true);

                $numVolteIscrittoClasse = $arrNumVolteIscrittoClasse[0]['count'];

            } else {
                $arrUltimaRiga['esito'] = null;
                $arrUltimaRiga['data_riferimento'] = null;
            }

            $trasferito = null;
            $dataTrasferimento = null;

            if (in_array(strtoupper($arrUltimaRiga['esito']),['TRASFERITO', 'TRASFERITO PRIMA DEL 15/3']))
            {
                // Trasferito
                $trasferito = '1';
                $dataTrasferimento = date('Y-m-d', $arrUltimaRiga['data_riferimento']);
            }
            else if (in_array(strtoupper($arrUltimaRiga['esito']),['RITIRATO', 'RITIRATO PRIMA DEL 15/3']))
            {
                // Ritirato
                $trasferito = '4';
                $dataTrasferimento = date('Y-m-d', $arrUltimaRiga['data_riferimento']);
            }
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Classe dello Studente">
            $query_classe = "SELECT classi_complete.*
                                FROM classi_complete
                                INNER JOIN classi_studenti ON (classi_complete.id_classe = classi_studenti.id_classe)
                                LEFT JOIN tempi_funzionamento ON (classi_complete.tempo_funzionamento = tempi_funzionamento.id_tempo_funzionamento
                                    AND tempi_funzionamento.flag_canc = 0)
                                WHERE classi_studenti.flag_canc = 0
                                    AND codice_ministeriale != 'FAKE'
                                    AND id_studente = {$id_studente}
                                ORDER BY ordinamento,
                                    classe,
                                    sezione,
                                    descrizione_indirizzi";

            $dati_classe_studente = $this->db->query($query_classe, true);

            $anno_corso = $dati_classe_studente[0]['classe'];
            $ore_settimanali = '';
            if (count($dati_classe_studente) > 1) {
                foreach ($dati_classe_studente as &$classe_studente) {
                    // Controlla se il codice ministeriale è 'mx02'
                    if ($classe_studente['codice_ministeriale'] == 'MX02') {
                        // Imposta il valore id_corso_ministeriale a 5
                        $override_corso = 6;
                    }
                }
                $dati_classe_studente[0]['id_corso_ministeriale'] = $override_corso;
            }


            if ($dati_classe_studente[0]['tipo_indirizzo'] == '1' && $dati_classe_studente[0]['codice_ministeriale'] == 'LI01')
            {
                switch ($dati_classe_studente[0]['classe'])
                {
                    case '4':
                        $anno_corso = 1;
                        break;
                    case '5':
                        $anno_corso = 2;
                        break;
                    case '1':
                        $anno_corso = 3;
                        break;
                    case '2':
                        $anno_corso = 4;
                        break;
                    case '3':
                        $anno_corso = 5;
                        break;
                }
            }

            if (in_array($dati_classe_studente[0]['tipo_indirizzo'], ['4', '6']))
            {
                $query_orario = "SELECT fine_fascia
                                    FROM tempi_funzionamento
                                    WHERE flag_canc = 0
                                        AND id_tempo_funzionamento = " . $dati_classe_studente[0]['tempo_funzionamento'];

                $dati_tempo_funzionamento = $this->db->query($query_classe, true);

                $ore_settimanali = $dati_tempo_funzionamento[0]['fine_fascia'];
            }

            $quadro_orario_obbligatorio = false;
            if (
                    (
                    !in_array($dati_classe_studente[0]['tipo_indirizzo'] ,['4','6','7'])
                    &&
                    $dati_classe_studente[0]['codice_ministeriale'] != 'FAKE'
                    )
                    &&
                    !in_array($dati_classe_studente[0]['classificazione_indirizzi'],['Q3','Q4','R3','PQ','R4'])
                )
            {
                $quadro_orario_obbligatorio = true;
            }
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Esoneri Religione ed Educazione Fisica">

            $studente['materia_sostitutiva_religione'] = $studente['materia_sostitutiva_religione'] != -1 ?: null;
            $studente['materia_sostitutiva_edfisica'] = $studente['materia_sostitutiva_edfisica'] != -1 ?: null;

            $query_esonero_religione = "SELECT materie.id_materia,
                                    tipo_materia,
                                    id_studente
                                FROM materie
                                    INNER JOIN classi_prof_materie ON (materie.id_materia = classi_prof_materie.id_materia)
                                    INNER JOIN classi_studenti ON (classi_studenti.id_classe = classi_prof_materie.id_classe)
                                WHERE tipo_materia IN ('RELIGIONE')
                                    AND materie.flag_canc = 0
                                    AND classi_prof_materie.flag_canc = 0
                                    AND classi_studenti.flag_canc = 0
                                    AND id_studente = {$id_studente}";

            $esoneri_studente_religione = $this->db->query($query_esonero_religione);

            // Religione
            if ($esoneri_studente_religione['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 1)
            {
                $esonero_religione = $esoneri_studente_religione;
            }
            else
            {
                $esonero_religione = ['id_studente' => null];
                $studente['materia_sostitutiva_religione'] = null;
            }

            $query_esonero_edfisica = "SELECT materie.id_materia,
                                    tipo_materia,
                                    id_studente
                                FROM materie
                                    INNER JOIN classi_prof_materie ON (materie.id_materia = classi_prof_materie.id_materia)
                                    INNER JOIN classi_studenti ON (classi_studenti.id_classe = classi_prof_materie.id_classe)
                                WHERE tipo_materia IN ('EDUCAZIONE FISICA')
                                    AND materie.flag_canc = 0
                                    AND classi_prof_materie.flag_canc = 0
                                    AND classi_studenti.flag_canc = 0
                                    AND id_studente = {$id_studente}";

            $esoneri_studente_edfisica = $this->db->query($query_esonero_edfisica);

            // Ed. Fisica
            if ($esoneri_studente_edfisica['tipo_materia'] == 'EDUCAZIONE FISICA' && $studente['esonero_ed_fisica'] == 1)
            {
                $esonero_ed_fisica = $esoneri_studente_edfisica;
            }
            else
            {
                $esonero_ed_fisica = ['id_studente' => null];
                $studente['materia_sostitutiva_edfisica'] = null;
            }
            //}}} </editor-fold>



            // Popolamento Array Curriculum

            $dati_stud[$id_studente] = [
                'cognome'           => $studente['cognome'],
                'nome'              => $studente['nome'],
                'codice_fiscale'    => $studente['codice_fiscale'],
                'quadro_orario'     => $quadro_orario_obbligatorio,
            ];

            // 20180929 Modificata origine sezione: prendiamo la sezione sidi recuperata da IMPORTA DATI ANAGRAFE

            $datiCurriculum[$id_studente] = [
                "data_iscrizione"                               => $dataIscrizione,
                "data_trasferimento"                            => $dataTrasferimento,
                "credito_scolastico"                            => "",
                "anno_di_corso"                                 => $anno_corso,
                "riservato_al_gestore_01"                       => "",
                "ore_settimanali"                               => "",
                "posizione_registro"                            => $studente['registro'],
                "riservato_al_gestore_02"                       => "",
                "riservato_al_gestore_03"                       => "",
                "numero_volte_iscrizione_classe"                => $numVolteIscrittoClasse,
                "identificativo_alunno"                         => $codici_scuola['codice_prenotazione'] . $studente['codice_fiscale'],
                "anno_scolastico"                               => explode("/", $annoScolatico['valore'])[0],
                "attivita_alternativa_1"                        => "",
                "attivita_alternativa_2"                        => "",
                "attivita_alternativa_3"                        => "",
                "attivita_alternativa_4"                        => "",
                "attivita_alternativa_5"                        => "",
                "attivita_alternativa_6"                        => "",
                "attivita_alternativa_7"                        => "",
                "attivita_alternativa_8"                        => "",
                "riservato_al_gestore_04"                       => "",
                "valore_credito_formativo"                      => "",
                "identificativo_corso"                          => $dati_classe_studente[0]['id_corso_ministeriale'],
                "identificativo_esito_finale"                   => "",
                "identificativo_prima_materia_esonerata"        => $esonero_religione['id_studente'] ? $codici_scuola['codice_prenotazione'] . $esonero_religione['id_studente'] : "",
                "identificativo_seconda_materia_esonerata"      => $esonero_ed_fisica['id_studente'] ? $codici_scuola['codice_prenotazione'] . $esonero_ed_fisica['id_studente'] : "",
                "identificativo_terza_materia_esonerata"        => "",
                "riservato_al_gestore_05"                       => "",
                "identificativo_fornito_di"                     => "",
                "flag_situazione_scolastica_attiva"             => is_null($trasferito) ? "1" : "0",
                "flag_anticipo_esame"                           => "",
                "riservato_al_gestore_06"                       => "",
                "flag_dopo_mensa"                               => "",
                "flag_esame_privatista"                         => "",
                "riservato_al_gestore_07"                       => "",
                "flag_mensa"                                    => $studente['servizio_mensa'],
                "flag_privatista"                               => $studente['stato_privatista'] == "NO" ? "0" : "1",
                "flag_trasferito"                               => $trasferito,
                "codice_giudizio_finale"                        => "",
                "descrizione_giudizio_sintetico"                => "",
                "riservato_al_gestore_08"                       => "",
                "identificativo_prima_materia_alternativa"      => $studente['materia_sostitutiva_religione'] ? $codici_scuola['codice_prenotazione'] . $studente['materia_sostitutiva_religione'] : "",
                "identificativo_seconda_materia_alternativa"    => $studente['materia_sostitutiva_edfisica'] ? $codici_scuola['codice_prenotazione'] . $studente['materia_sostitutiva_edfisica'] : "",
                "identificativo_terza_materia_alternativa"      => "",
                "codice_primo_mezzo_trasporto"                  => "",
                "codice_secondo_mezzo_trasporto"                => "",
                "giudizio_di_non_ammissione"                    => "",
                "descrizione_note"                              => "",
                "identificativo_obbligo_formativo"              => $studente['obbligo_formativo'] != "" ? $codici_scuola['codice_prenotazione'] . $studente['obbligo_formativo'] : "",
                "identificativo_orario_settimanale"             => $ore_settimanali,
                "postscuola"                                    => "",
                "prescuola"                                     => "",
                "progressivo_curriculum"                        => $progressivoCurr,
                "riservato_al_gestore_09"                       => "",
                "identificativo_piano_di_studio_1"              => $studente['id_quadro_orario_sidi'],
                "identificativo_piano_di_studio_2"              => "",
                "identificativo_scuola"                         => $codici_scuola['codice_prenotazione'] . $this->formatId($studente['id_scuola']),
                "riservato_al_gestore_10"                       => "",
                "sezione"                                       => $dati_classe_studente[0]['sezione_sidi'], //substr($dati_classe_studente[0]['sezione'], 0, 4),
                "riservato_al_gestore_11"                       => "",
                "riservato_al_gestore_12"                       => "",
                "riservato_al_gestore_13"                       => "",
                "riservato_al_gestore_14"                       => "",
                "validita_anno_scolastico"                      => "",
                "data_inizio_frequenza"                         => $data_inizio_frequenza,
                "prima_frazione_temporale_alunno"               => "",
                "rientro_pomeridiano"                           => "",
                "identificativo_sede"                           => $dati_classe_studente[0]['id_sede_sidi'],
                "indicatore_esame_idonetà"                      => "",
            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiCurriculum)) {
                foreach ($datiCurriculum as $id => $alunno)
                {
                    $checkCurriculum = $this->checkCurriculumData($dati_stud[$id], $alunno);

                    if (!empty($checkCurriculum['errors']))
                    {
                        $errors['errors']['codice_sidi'][] = $checkCurriculum['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiCurriculum;
        }
	//}}} </editor-fold>
    }

    /**
     * Restituisce i dati degli alunni frequentanti all'estero
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getFrequentantiEstero($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">
        $datiFrequentantiEstero = [];

        $codici_scuola = $this->getCodiciScuola($meccanografico);

        $mecc = explode('-', $meccanografico);
        $annoScolatico = $this->getAnnoScolastico();

        $query_studenti = "SELECT DISTINCT ON (id_studente)
                                id_studente,
                                cognome,
                                nome,
                                codice_fiscale,
                                id_quadro_orario_sidi,
                                id_scuola
                            FROM studenti_completi
                            WHERE codice_ministeriale != 'AA'
                                AND ordinamento = '0'
                                AND classificazione_indirizzi NOT IN ('SE')
                                AND id_codice_ministeriale NOT IN (94)
                                AND esito_corrente_calcolato not like 'Trasferit%'
                                AND esito_corrente_calcolato not like 'Ritirato%'
                                AND esito_corrente_calcolato = 'Frequenza anno estero'
                                AND descrizione_indirizzi not ilike '%RITIR%'
                                AND descrizione_indirizzi not ilike '%TRASFE%'
                                AND descrizione_indirizzi not ilike '%PREISCR%'
                                AND codice_meccanografico = '{$mecc[0]}'
                                AND codice_meccanografico_secondario = '{$mecc[1]}'
                                AND classe != '-1'";

        $arrStudenti = $this->db->query($query_studenti, true);

        foreach ($arrStudenti as $studente)
        {
            $id_studente = $studente['id_studente'];

            // Curriculum Studente da storia studenti
            $query_studente = "SELECT *
                        FROM storia_studenti
                        WHERE id_studente = {$id_studente}
                            AND flag_canc = 0
                            AND anno_scolastico = '{$annoScolatico['valore']}'
                            AND esito = 'Frequenza anno estero'";

            $arrCurrStudente = $this->db->query($query_studente, true);

            $subquery_progr_curr = "SELECT DISTINCT esito, anno_scolastico
                                    FROM storia_studenti
                                    WHERE id_studente = {$id_studente}
                                        AND flag_canc = 0
                                        AND id_scuola IN ('" . $mecc[0] . "', '" . $mecc[1] . "', '')
                                        AND upper(esito) = 'ISCRITTO'";

            $query_progr_curr = "SELECT COUNT(*) FROM ($subquery_progr_curr) AS progressivo_curr";

            $dati_progr_curr = $this->db->query($query_progr_curr, true);

            $progressivoCurr = str_pad($dati_progr_curr[0]['count'], 4, '0', STR_PAD_LEFT);
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Iscrizione + classe tradotta">
            $tipo_frequenza_estero = null;
            foreach ($arrCurrStudente as $idx => $singleCurr)
            {
                $tipo_frequenza_estero = $singleCurr['motivazione_interruzione'];
                $data_inizio_frequenza_estero = date('Y-m-d', $singleCurr['data_riferimento']);
                if($singleCurr['data_fine_frequenza_estero'] > 0)
                {
                    $data_fine_frequenza_estero = date('Y-m-d', $singleCurr['data_fine_frequenza_estero']);
                }
                else
                {
                    $data_fine_frequenza_estero = 0;
                }
                $stato_frequenza_estero = $singleCurr['stato_frequenza_estero'];
                $citta_frequenza_estero = $singleCurr['citta_frequenza_estero'];
                $scuola_frequenza_estero = $singleCurr['scuola_frequenza_estero'];
            }
            //}}} </editor-fold>

            // Popolamento Array Curriculum

            $dati_stud[$id_studente] = [
                'cognome'           => $studente['cognome'],
                'nome'              => $studente['nome'],
                'codice_fiscale'    => $studente['codice_fiscale'],
            ];

            $datiFrequentantiEstero[$id_studente] = [
                "identificativo_scuola"                 => $codici_scuola['codice_prenotazione'] . $this->formatId($studente['id_scuola']),
                "identificativo_alunno"                 => $codici_scuola['codice_prenotazione'] . $studente['codice_fiscale'],
                "progressivo_curriculum"                => $progressivoCurr,
                "anno_scolastico"                       => explode("/", $annoScolatico['valore'])[0],
                "tipo_frequenza_estero"                 => $tipo_frequenza_estero,
                "data_inizio_frequenza_estero"          => $data_inizio_frequenza_estero,
                "data_fine_frequenza_estero"            => $data_fine_frequenza_estero,
                "stato_frequenza_estero"                => $stato_frequenza_estero,
                "citta_frequenza_estero"                => $citta_frequenza_estero,
                "scuola_frequenza_estero"               => $scuola_frequenza_estero,
            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiFrequentantiEstero)) {
                foreach ($datiFrequentantiEstero as $id => $alunno)
                {
                    $checkFrequentantiEstero = $this->checkFrequentantiEsteroData($dati_stud[$id], $alunno);

                    if (!empty($checkFrequentantiEstero['errors']))
                    {
                        $errors['errors']['codice_sidi'][] = $checkFrequentantiEstero['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiFrequentantiEstero;
        }
	//}}} </editor-fold>
    }

    /**
     * Verifica se lo studente ha i dati corretti
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student) {
        $errors = ['errors' => ''];
                file_put_contents("/tmp/stfs", print_r($student, true), FILE_APPEND);

        if (($student['codice_cap_residenza'] == '' || strlen($student['codice_cap_residenza']) <> 5) && $student['codice_comune_residenza'] != 'EEEE') {
            $errors['errors'] = "Errore: errore sul CAP di residenza per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if ($student['codice_comune_residenza'] == ''|| $student['codice_comune_residenza'] == '------' || strlen($student['codice_comune_residenza']) <> 4) {
            $errors['errors'] = "Errore: errore sul comune di residenza per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if (strlen($student['cognome']) > 100) {
            $errors['errors'] = "Errore: il cognome dello studente {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']} è troppo lungo.<br>La lunghezza massima è di 100 caratteri";
        }

        if (strlen($student['nome']) > 100) {
            $errors['errors'] = "Errore: il nome dello studente {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']} è troppo lungo.<br>La lunghezza massima è di 100 caratteri";
        }

        if (strlen($student['indirizzo_residenza']) > 50) {
            $errors['errors'] = "Errore: Indirizzo di residenza troppo lungo per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}<br>La lunghezza massima è di 50 caratteri";
        }

        if ($student['indirizzo_residenza'] == '') {
            $errors['errors'] = "Errore: errore sull'indirizzo di residenza per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if ($student['codice_prima_cittadinanza'] == '') {
            $errors['errors'] = "Errore: manca la cittadinanza per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if ($student['data_di_nascita'] == '') {
            $errors['errors'] = "Errore: manca la data di nascita per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if ($student['stato_nascita'] != "" && strlen($student['stato_nascita']) != 3)
        {
            $errors['errors'] = "Errore: stato di nascita errato per {$student['cognome_alunno']} {$student['nome']}";
        }

        if ($student['cittadinanza'] != "" && strlen($student['cittadinanza']) != 3)
        {
            $errors['errors'] = "Errore: prima cittadinanza errata per {$student['cognome_alunno']} {$student['nome']}";
        }

        if ($student['seconda_cittadinanza'] != "" && strlen($student['seconda_cittadinanza']) != 3)
        {
            $errors['errors'] = "Errore: seconda cittadinanza errata per {$student['cognome_alunno']} {$student['nome']}";
        }

        if ($student['stato_di_nascita'] == '200' && $student['codice_comune_nascita_alunno'] == '') {
            $errors['errors'] = "Errore: manca il comune di nascita per lo studente {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if ($student['stato_di_nascita'] == '') {
            $errors['errors'] = "Errore: manca lo stato di nascita per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if ($student['luogo_nascita_estero'] == '' && $student['stato_di_nascita'] != '200') {
            $errors['errors'] = "Errore: manca la città straniera di nascita per {$student['cognome_alunno']} {$student['nome']} codice fiscale: {$student['codice_fiscale']}";
        }

        if (strlen($student['codice_fiscale']) != 16) {
            $errors['errors'] = "Errore: codice fiscale errato per {$student['cognome_alunno']} {$student['nome']}";
        }

        if ($student['codice_fiscale'] == '' || $student['codice_fiscale'] == 'NON CALCOLABILE NESSUN RISULTATO' ) {
            $errors['errors'] = "Errore: manca il codice fiscale per {$student['cognome_alunno']} {$student['nome']}";
        }
        file_put_contents("/tmp/errrors", print_r($errors, true),FILE_APPEND);
        return $errors;
    }

    /**
     * Verifica se i curriculum hanno i dati corretti
     *
     * @param array $student
     * @return string
     */
    public function checkCurriculumData($studente, $cv) {
        $errors = ['errors' => ''];

        if ($cv['anno_di_corso'] == '') {
            $errors['errors'] = "Errore: manca l'anno di corso per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}";
        }

        if ($cv['sezione'] == '') {
            $errors['errors'] = "Errore: manca la sezione per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}";
        }

        if ($cv['identificativo_sede'] == '') {
            $errors['errors'] = "Errore: sede non associata allo studente {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} - Effettuare nuovamente l'operazione IMPORTA DATI ANAGRAFE sa Setup C06 e salvare.";
        }

        if ($cv['progressivo_curriculum'] == '') {
            $errors['errors'] = "Errore: nessuna riga di curriculum presente per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}";
        }

        if ($cv['identificativo_piano_di_studio_1'] == '' && $studente['quadro_orario'] == true) {
            $errors['errors'] = "Errore: nessun piano di studio abbinato allo studente {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}";
        }

        return $errors;
    }

    /**
     * Verifica se i frequentanti estero hanno i dati corretti
     *
     * @param array $student
     * @return string
     */
    public function checkFrequentantiEsteroData($studente, $cv) {
        $errors = ['errors' => ''];

        if ($cv['tipo_frequenza_estero'] == 0) {
            $errors['errors'] .= "Errore: manca la motivazione interruzione per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        if ($cv['data_inizio_frequenza_estero'] == 0) {
            $errors['errors'] .= "Errore: manca la data inizio frequenza estero per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        if ($cv['data_fine_frequenza_estero'] == 0) {
            $errors['errors'] .= "Errore: manca la data fine frequenza estero per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        if ($cv['stato_frequenza_estero'] == '') {
            $errors['errors'] .= "Errore: manca lo stato di frequenza all'estero per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        if ($cv['citta_frequenza_estero'] == '') {
            $errors['errors'] .= "Errore: manca la città di frequenza all'estero per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        if (strlen($cv['citta_frequenza_estero']) > 200) {
            $errors['errors'] .= "Errore: la città di frequenza all'estero è troppo lunga per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        if ($cv['scuola_frequenza_estero'] == '') {
            $errors['errors'] .= "Errore: manca la scuola di frequenza all'estero per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        if (strlen($cv['scuola_frequenza_estero']) > 200) {
            $errors['errors'] .= "Errore: la scuola di frequenza all'estero è troppo lunga per {$studente['cognome']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} <br>";
        }

        return $errors;
    }

    /**
     * Restituisce l'ID ed il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico)
    {
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola, codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4) {
        if (!isset($id) || trim($id) === '') {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++) {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    }

    private function anagrafe_nazionale($meccanografico) {

        ini_set('memory_limit', '4096M');

        $datiFinali = [];
        $dati_finali_scuole = [];
        $dati_finali_studenti = [];
        $dati_finali_curriculum = [];

        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];

        $timestamp = date('YmdHi');
        $mecc = explode('-', $meccanografico);
        $annoScolatico = $this->getAnnoScolastico();
        $codici_scuola = $this->getCodiciScuola($meccanografico);

        // File Scuola
        // ---------------------
        $datiScuola = $this->getScuola($meccanografico);
        foreach ($datiScuola as $id_scuola => $scuola)
        {
            $dati_finali_scuole[$id_scuola] = implode("|", $scuola) . "|\n";
        }

        // File Studenti
        // ---------------------
        $datiStudente = $this->getStudenti($meccanografico);
        foreach ($datiStudente as $id_studente => $studente)
        {
            $dati_finali_studenti[$id_studente] = implode("|", $studente) . "|\n";
        }

        // File Curriculum
        // ---------------------
        $datiCurriculum = $this->getCurriculum($meccanografico);
        foreach ($datiCurriculum as $id_curriculum => $curriculum)
        {
            $dati_finali_curriculum[$id_curriculum] = implode("|", $curriculum) . "|\n";
        }

        // File Frequentanti Estero
        // ---------------------
        $datiFrequentantiEstero = $this->getFrequentantiEstero($meccanografico);
        foreach ($datiFrequentantiEstero as $id_frequentante_estero => $dati_frequentante_estero)
        {
            $dati_finali_frequentanti_estero[$id_frequentante_estero] = implode("|", $dati_frequentante_estero) . "|\n";
        }

        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolatico['valore'])[0] .
                        "00NF" .
                        "MAST" .
                        "20160930" .
                        $timestamp;
        if(is_array($dati_finali_frequentanti_estero))
        {
            $datiFinali = [
                    'scuole' => array_values($dati_finali_scuole),
                    'studenti' => array_values($dati_finali_studenti),
                    'curriculum' => array_values($dati_finali_curriculum),
                    'frequentanti_estero' => array_values($dati_finali_frequentanti_estero),
                ];
        }
        else
        {
            $datiFinali = [
                    'scuole' => array_values($dati_finali_scuole),
                    'studenti' => array_values($dati_finali_studenti),
                    'curriculum' => array_values($dati_finali_curriculum),
                ];
        }

        if (empty($result[$meccanografico]['errors_stop']))
        {
            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale/*00NFMAST20160930*");

            // Creo il file Scuole
            $filename_scuole = $prefisso_file . '001TB_USR_USER.TXT';
            foreach ($datiFinali['scuole'] as $riga_scuola)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/" . $filename_scuole, print_r($riga_scuola, true), FILE_APPEND);
            }

            // Creo il file Studenti
            $filename_studenti = $prefisso_file . '002TB_ALU_ALUNNI.TXT';
            foreach ($datiFinali['studenti'] as $riga_studente)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/" . $filename_studenti, print_r($riga_studente, true), FILE_APPEND);
            }

            // Creo il file Curriculum
            $filename_curriculum = $prefisso_file . '003TB_ALU_SIT.TXT';
            foreach ($datiFinali['curriculum'] as $riga_curriculum)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/" . $filename_curriculum, print_r($riga_curriculum, true), FILE_APPEND);
            }

            // Creo il file Frequentanti estero
            $filename_frequentanti_estero = $prefisso_file . '004TB_ALU_FRQEST.TXT';
            if(!empty($datiFinali['frequentanti_estero']))
            {
                foreach ($datiFinali['frequentanti_estero'] as $riga_frequentante_estero)
                {
                    file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/" . $filename_frequentanti_estero, print_r($riga_frequentante_estero, true), FILE_APPEND);
                }
            }


            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/NUOVOAVVIOSCUOLA_' . explode('-',$meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile) {
                if ($zip->open($zipFile) === TRUE) {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/anagrafe_nazionale/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                            or die ("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale/##*00NFMAST20160930*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));
        file_put_contents('/tmp/datiFinali', print_r($datiFinali, true));

        return $result[$meccanografico]['errors_stop'];
    }
}
