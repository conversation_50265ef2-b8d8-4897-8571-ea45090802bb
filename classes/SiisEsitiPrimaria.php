<?php

class SiisEsitiPrimaria extends Siis
{

    protected $db;
    protected $pdf;
    public $flusso;
    public $versione;
    public $cartella;
    public $prefisso_flusso;

    function __construct($flusso, $database)
    {
        $this->flusso_sidi = $flusso;
        $this->database = $database;

        parent::__construct($this->flusso, $this->database);

        $pdf = new PDFClass();
        $this->pdf = $pdf;

        $this->flusso = "00EE";
        $this->versione = "20180528";
        $this->cartella = "esiti_primaria/";
        $this->prefisso_flusso = "ESITI_PRIMARIA";
    }

    function decode($field)
    {
        if (!is_array($field) && strlen($field) > 0)
        {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        }
        else
        {
            return $field;
        }
    }

    public function normalize_string($string)
    {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     */
    public function createZip($fileName, $codMecc, $timestamp = null)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;

        exec("rm /var/www-source/mastercom/tmp_siis/" . $this->cartella . $this->prefisso_flusso . "_{$codMecc}_{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/". $this->cartella ."{$fileName}*.TXT");

        foreach ($glob as $file)
        {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/" . $this->cartella . $this->prefisso_flusso . "_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE)
            {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/". $this->prefisso_flusso . "*.TXT");
    //}}} </editor-fold>
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $codMecc
     */
    public function mergeFile($codMecc)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $codMecc = explode('-',$codMecc)[1];
        $timestamp = date('Ymd');

        $annoScolastico = $this->getAnnoScolastico();
        $glob = glob("/var/www-source/mastercom/tmp_siis/" . $this->cartella . $this->prefisso_flusso . "_{$codMecc}_{$timestamp}*.zip");

        foreach ($glob as $key => $file)
        {
            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/". $this->cartella . "{$basename}";

            if ($zip->open($nomeZip) == TRUE)
            {
                if ($key == 1)
                {
                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/'. $this->cartella);
                    $zip->close();

                    $txt1 = glob("/var/www-source/mastercom/tmp_siis/". $this->cartella . "*{$codMecc}*.txt");

                    foreach ($txt1 as $value)
                    {
                        $basename1 = basename($value);
                        exec("mv /var/www-source/mastercom/tmp_siis/". $this->cartella . "{$basename1} /var/www-source/mastercom/tmp_siis/". $this->cartella . "{$basename1}_1.txt");
                    }
                }
                else
                {
                    exec("mkdir /var/www-source/mastercom/tmp_siis/". $this->cartella . "tmp/");

                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/". $this->cartella . "/tmp/');
                    $zip->close();
                }
            }
            else
            {
                echo 'Merge files failed';
            }
        }

        $txt = glob("/var/www-source/mastercom/tmp_siis/". $this->cartella . "tmp/*{$codMecc}*.txt");

        foreach ($txt as $value)
        {
            $basename = basename($value);
            exec("mv /var/www-source/mastercom/tmp_siis/". $this->cartella . "tmp/{$basename} /var/www-source/mastercom/tmp_siis/". $this->cartella . "tmp/{$basename}_2.txt");
            exec("mv /var/www-source/mastercom/tmp_siis/". $this->cartella . "tmp/{$basename}_2.txt /var/www-source/mastercom/tmp_siis/". $this->cartella);
        }

        $txt2 = glob("/var/www-source/mastercom/tmp_siis/". $this->cartella . "*{$codMecc}*.txt");

        $finalName = $codMecc . explode('/', $annoScolastico['valore'])[0] . substr(explode('/', $annoScolastico['valore'])[1], 2, 2)  . $this->flusso . "MAST". $this->versione;

        foreach ($txt2 as $value)
        {
            $file = file_get_contents($value);
            file_put_contents("/var/www-source/mastercom/tmp_siis/". $this->cartella . "{$finalName}.txt", print_r($file, true), FILE_APPEND);
        }

        exec("/var/www-source/mastercom/tmp_siis/". $this->cartella . "*{$codMecc}*.txt_*.txt");
        exec("mv /var/www-source/mastercom/tmp_siis/". $this->cartella . $this->prefisso_flusso . "_{$codMecc}_{$timestamp}*.zip /var/www-source/mastercom/tmp_siis/". $this->cartella . "/tmp/");

        $this->createZip($finalName, $codMecc);
    //}}} </editor-fold>
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        if (!isset($id) || trim($id) === '')
        {
            return null;
        }

        $zeroString = '';
        for ($i = 0; $i < $zeros; $i++)
        {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    //}}} </editor-fold>
    }

    /**
     * Restituisce l'ID ed il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola,
                        codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
    //}}} </editor-fold>
    }

    /**
     * Restituisce i dati della scuola in questione
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT scuole.id_scuola,
                            scuole.descrizione,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            scuole.codice_prenotazione,
                            sedi.id_sede,
                            sedi.descrizione as sedi_descrizione
                        FROM scuole,
                            sedi,
                            indirizzi
                        WHERE scuole.id_scuola = sedi.id_scuola::int
                            AND sedi.id_sede = indirizzi.id_sede::bigint
                            AND indirizzi.id_codice_ministeriale IN (
                                SELECT id_indirizzo FROM indirizzi_ministeriali
                                    WHERE classificazione IN ('EE')
                            )
                            AND indirizzi.tipo_indirizzo IN ('6')
                            AND indirizzi.flag_canc = 0
                            AND scuole.flag_canc = 0
                            AND sedi.flag_canc = 0
                            AND codice_meccanografico = '{$mecc[0]}'
                            AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $datiScuola =  $this->db->query($query);

        if (array_key_exists(0, $datiScuola))
        {
            foreach ($datiScuola as $value)
            {
                if ($value['codice_prenotazione'] == '')
                {
                    $errors['errors']['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$value['codice_meccanografico']}-{$value['codice_meccanografico_secondario']}";

                    if (!empty($errors['errors']))
                    {
                        $datiScuola['errors'] = $errors['errors'];
                    }
                }
            }
        }
        else
        {
            if ($datiScuola['codice_prenotazione'] == '')
            {
                $errors['errors']['codice_prenotazione'] = "Errore: manca il codice prenotazione per la scuola {$datiScuola['codice_meccanografico']}-{$datiScuola['codice_meccanografico_secondario']}";

                if (!empty($errors['errors']))
                {
                    $datiScuola['errors'] = $errors['errors'];
                }
            }
        }

        return $datiScuola;
    //}}} </editor-fold>
    }

    /**
     * Estrae l'elenco degli studenti per la scuola in questione con i seguenti
     * filtri:
     *
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];
        $mecc = explode('-', $meccanografico);
        $annoScolastico = $this->getAnnoScolastico();

        $query = "SELECT DISTINCT id_studente,
                        codice_alunno_ministeriale,
                        cognome as cognome_alunno,
                        nome,
                        codice_fiscale,
                        classe,
                        sezione,
                        sezione_sidi,
                        descrizione_indirizzi,
                        esito_corrente_calcolato,
                        codice_meccanografico,
                        codice_meccanografico_secondario,
                        CASE
                            WHEN classe = '1' THEN esito_prima_elementare
                            WHEN classe = '2' THEN esito_seconda_elementare
                            WHEN classe = '3' THEN esito_terza_elementare
                            WHEN classe = '4' THEN esito_quarta_elementare
                            WHEN classe = '5' THEN esito_quinta_elementare
                            else ''
                        END as esito_primaria
                    FROM studenti_completi
                    WHERE codice_ministeriale <> 'FAKE'
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                        AND classificazione_indirizzi = 'EE'
                        AND tipo_indirizzo IN ('6') AND ordinamento = '0'
                ";

        $arrAlunno = $this->db->query($query);

        $dati_studenti = $dati_esiti_studente = [];

        foreach ($arrAlunno as $alunno_singolo)
        {
            $id_studente = $alunno_singolo['id_studente'];
            $esito_studente = $alunno_singolo['esito_primaria'];

            $dati_studenti[$id_studente] = $alunno_singolo;

            switch($esito_studente)
            {
                case "SI":
                    $esito_primaria = 22;
                    break;
                case "NO":
                default:
                    $esito_primaria = 27;
                    break;
            }

            $dati_esiti[$id_studente] = [
                "codice_ministeriale_scuola"    => $alunno_singolo['codice_meccanografico_secondario'],
                "anno_scolastico"               => explode('/', $annoScolastico['valore'])[0],
                "codice_alunno_sidi"            => $alunno_singolo['codice_alunno_ministeriale'],
                "codice_esito_sidi"             => $esito_primaria,
            ];

        }

        if ($check)
        {
            if (is_array($dati_esiti))
            {
                foreach ($dati_esiti as $id => $alunno)
                {
                    $checkStudente = $this->checkStudentsData($alunno, $dati_studenti[$id]);

                    if (!empty($checkStudente['errors']))
                    {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
            else
            {
                $errors['errors']['nessun_dato_presente'][] = "Non sono presenti dati da elaborare per la scuola {$mecc[0]}-{$mecc[1]}";
            }
        }

        if (!empty($errors['errors']))
        {
            return $errors;
        }
        else
        {
            return $dati_esiti;
        }
    //}}} </editor-fold>
    }

    /**
     * Verifica se lo studente ha il codice SIDI settato
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student, $stud_data)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];

        if ($student['codice_alunno_sidi'] == '')
        {
            $errors['errors'] = "Errore: manca il codice SIDI per {$stud_data['cognome_alunno']} {$stud_data['nome']} classe {$stud_data['classe']}{$stud_data['sezione']}. "
                . "<br> Per scaricare il codice SIDI andare in Setup C06 - ESTRAZIONE ALUNNI SOGEI, inserire le credenziali, selezionare l'istituto e premere su Procedere";
        }

        if (strlen($student['anno_scolastico']) != 4)
        {
            $errors['errors'] = "Errore: anno scolastico errato per {$stud_data['cognome_alunno']} {$stud_data['nome']} classe {$stud_data['classe']}{$stud_data['sezione']}";
        }

        return $errors;
    //}}} </editor-fold>
    }

    /**
     * Recupera le assenze degli studenti selezionati
     *
     * @param type $dati_studente
     * @param type $check
     * @return type
     */
    public function getAssenze($dati_studente, $check = null)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $annoScolastico = $this->getAnnoScolastico();

        $datiAssenze = [];
        // Ciclo gli studenti e tiro fuori le loro assenze
        if (!empty($dati_studente))
        {
            foreach ($dati_studente as $id_studente => $studente)
            {
                //$id_studente = $studente['id_studente'];

                $query_assenze = "SELECT id_studente,
                                        EXTRACT (year FROM to_timestamp(data)) AS anno,
                                        EXTRACT (month FROM to_timestamp(data)) AS mese,
                                        tipo_assenza,
                                        count(*) as totale
                                    FROM assenze
                                    WHERE data <= " . time() ."
                                        AND id_studente = {$id_studente}
                                    GROUP BY
                                        id_studente,
                                        anno,
                                        mese,
                                        tipo_assenza
                                    ORDER BY id_studente,
                                        anno,
                                        mese,
                                        tipo_assenza
                                    ";

                $arrAssenze = $this->db->query($query_assenze, true);

                foreach($arrAssenze as $assenza)
                {
                    switch ($assenza['tipo_assenza'])
                    {
                        // assenze
                        case 1:
                            $gruppo_assenze = 'A';
                            break;

                        // entrate + assenza mattino
                        case 2:
                        case 6:
                        case 8:
                        case 12:
                            $gruppo_assenze = 'R';
                            break;

                        // uscite +  assenza pomeriggio
                        case 3:
                        case 7:
                        case 9:
                        case 18:
                            $gruppo_assenze = 'U';
                            break;

                        default:
                            $gruppo_assenze = 'X';
                            break;
                    }

                    $valore_anno_scolastico = explode('/', $annoScolastico['valore'])[0] . substr(explode('/', $annoScolastico['valore'])[1], 2, 2);
                    $chiave_assenza = $studente['codice_alunno_sidi'] . "-" . $assenza['anno'] . $assenza['mese'] . $gruppo_assenze;

                    $datiAssenze[$chiave_assenza]['codice_ministeriale_scuola'] = $studente['codice_ministeriale_scuola'];
                    $datiAssenze[$chiave_assenza]['anno_scolastico'] = $valore_anno_scolastico;
                    $datiAssenze[$chiave_assenza]['codice_alunno_sidi'] = $studente['codice_alunno_sidi'];
                    $datiAssenze[$chiave_assenza]['mese'] = $this->formatId($assenza['mese'], 2);
                    $datiAssenze[$chiave_assenza]['tipo_rilevazione'] = $gruppo_assenze;
                    $datiAssenze[$chiave_assenza]['numero_giorni'] += $assenza['totale'];
                }
            }
        }
        else
        {
            $datiAssenze = [];
        }
        // verifico i dati
        if ($check)
        {
            if (is_array($datiAssenze))
            {
                foreach ($datiAssenze as $id => $assenze)
                {
                    $checkAssenza = $this->checkDatiAssenze($dati_studente[$assenze['codice_alunno_sidi']], $assenze);

                    if (!empty($checkAssenza['errors']))
                    {
                        $errors['errors']['codice_sidi'][] = $checkAssenza['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors']))
        {
            return $errors;
        }
        else
        {
            return $datiAssenze;
        }
    //}}} </editor-fold>
    }

   /**
     * Verifica se l'assenza ha i dati corretti
     *
     * @param array $studente
     * @param array $assenze
     * @return string
     */
    public function checkDatiAssenze($studente, $assenze)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];

        if ($assenze['codice_ministeriale_scuola'] == "")
        {
            $errors['errors'] = "Errore: codice alunno SIDI mancante per lo studente {$studente['cognome_alunno']} {$studente['nome']}.";
        }

        // SIDI vuole come anno scolastico anno di inizio a 4 cifre, anno di fine a 2 cifre (es. 201718)
        if (strlen($assenze['anno_scolastico']) != 6 || !is_numeric($assenze['anno_scolastico']))
        {
            $errors['errors'] = "Errore: anno scolastico errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if ($assenze['codice_alunno_sidi'] == '' || !is_numeric($assenze['codice_alunno_sidi']))
        {
            $errors['errors'] = "Errore: codice SIDI errato o assente per lo studente {$studente['cognome_alunno']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']} - {$studente['id_studente']}"
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if (strlen($assenze['mese']) != 2 || !is_numeric($assenze['mese']))
        {
            $errors['errors'] = "Errore: mese errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if ($assenze['tipo_rilevazione'] == '' || strlen($assenze['tipo_rilevazione']) != 1)
        {
            $errors['errors'] = "Errore: codice tipo rilevazione errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if (!is_numeric($assenze['numero_giorni'])) {
            $errors['errors'] = "Errore: totale mensile errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        return $errors;
    //}}} </editor-fold>
    }

    /**
     * Estrae le competenze certificate per gli studenti delle terze delle Primarie
     * @param type $dati_studente
     * @param type $check
     * @return type
     */
    public function getCompetenze($dati_studente)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $annoScolastico = $this->getAnnoScolastico();

        $datiCompetenze = [];
        // Ciclo gli studenti e tiro fuori le loro assenze
        if (!empty($dati_studente))
        {
            foreach ($dati_studente as $id_studente => $studente)
            {
                //$id_studente = $studente['id_studente'];

                // Estraggo le competenze dello studente
                $query = "SELECT v.id_studente,
                                v.id_competenza_scolastica,
                                c.codice_competenza_sidi,
                                v.valore,
                                v.testo
                            FROM competenze_valori v,
                                competenze_scolastiche c
                            WHERE v.id_competenza_scolastica = c.id_competenza_scolastica
                                AND v.flag_canc = 0
                                AND c.flag_canc = 0
                                AND c.anno_fine = 0
                                AND c.anno_inizio = 2023
                                AND c.ordine_scolastico in ('EE')
                                AND
                                (
                                    (v.valore != '' AND codice_competenza_sidi != 99)
                                    OR
                                    (v.testo != '' AND codice_competenza_sidi = 99)
                                )
                                AND v.id_studente = {$id_studente}
                            ORDER BY v.id_studente,
                                c.codice_competenza_sidi
                        ";
                $arrCompetenze = $this->db->query($query, true);

                foreach($arrCompetenze as $competenza)
                {
                    $chiave = $id_studente . $competenza['codice_competenza_sidi'].$competenza['valore'].$competenza['testo'];
                    $valore_anno_scolastico = explode('/', $annoScolastico['valore'])[0] . substr(explode('/', $annoScolastico['valore'])[1], 2, 2);
                    $codice_lingua = '';
                    if($competenza['codice_competenza_sidi'] == 18)
                    {
                        switch ($competenza['testo']) {
                            case "FRANCESE":
                                $codice_lingua = 'FR';
                                break;
                            case "SPAGNOLO":
                                $codice_lingua = 'SP';
                                break;
                            case "TEDESCO":
                                $codice_lingua = 'TE';
                                break;
                            case "INGLESE":
                                $codice_lingua = 'IN';
                                break;
                            case "RUSSO":
                                $codice_lingua = 'RU';
                                break;
                            case "ARABO":
                                $codice_lingua = 'AR';
                                break;
                            case "CINESE":
                                $codice_lingua = 'CI';
                                break;
                            case "EBRAICO":
                                $codice_lingua = 'EB';
                                break;
                            case "GIAPPONESE":
                                $codice_lingua = 'GI';
                                break;
                            case "ALBANESE":
                                $codice_lingua = 'AL';
                                break;
                            case "SLOVENO":
                                $codice_lingua = 'SL';
                                break;
                            case "SERBO-CROATO":
                                $codice_lingua = 'SC';
                                break;
                            case "NEOGRECO":
                                $codice_lingua = 'NE';
                                break;
                            case "PORTOGHESE":
                                $codice_lingua = 'PO';
                                break;
                            case "INGLESE POTENZIATO":
                                $codice_lingua = 'IP';
                                break;

                            default:
                                $codice_lingua = '';
                                break;
                        }
                    }
                    // Riga competenza
                    $datiCompetenze[$chiave]['codice_ministeriale_scuola']  = $studente['codice_ministeriale_scuola'];
                    $datiCompetenze[$chiave]['anno_scolastico']             = $valore_anno_scolastico;
                    $datiCompetenze[$chiave]['codice_alunno_sidi']          = $studente['codice_alunno_sidi'];
                    $datiCompetenze[$chiave]['codice_competenza']           = $competenza['codice_competenza_sidi'];
                    $datiCompetenze[$chiave]['certificazione_competenza']   = ($competenza['codice_competenza_sidi'] == 99) ? preg_replace( "/\r|\n/", "", substr($this->decode($competenza['testo']), 0, 256)) : $competenza['valore'];
                }
            }
        }
        return $datiCompetenze;
    //}}} </editor-fold>
    }

    /**
     * Genera i dati per il flusso
     * @param type $meccanografico
     * @return string
     */

    private function esitiPrimaria($meccanografico)
    {
        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];
        $datiFlusso = [];
        $mecc = explode('-', $meccanografico);
        $annoScolastico = $this->getAnnoScolastico();

        $dati_finali_esiti = $dati_finali_assenze = $dati_finali_competenze = [];

        // estraggo gli esiti
        $datiStudente = $this->getStudenti($meccanografico);
        foreach ($datiStudente as $id_studente => $studente)
        {
            $dati_finali_esiti[$id_studente] = implode("|", $studente) . "|\n";
        }
        $datiFinaliEsiti = array_values($dati_finali_esiti);

        // estraggo le assenze
        $datiAssenze = $this->getAssenze($datiStudente);
        foreach ($datiAssenze as $id_assenza => $assenze)
        {
            $dati_finali_assenze[$id_assenza] = implode("|", $assenze) . "|\n";
        }
        $datiFinaliAssenze = array_values($dati_finali_assenze);

        // estraggo le assenze
        $datiCompetenze = $this->getCompetenze($datiStudente);
        foreach ($datiCompetenze as $id_competenza => $competenza)
        {
            $dati_finali_competenze[$id_competenza] = implode("|", $competenza) . "|\n";
        }
        $datiFinalicompetenze = array_values($dati_finali_competenze);

        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $timestamp = date('YmdHi');

        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolastico['valore'])[0] .
                        $this->flusso .
                        "MAST" .
                        $this->versione .
                        $timestamp;

        // Genero i file necessari

        // . $this->cartella . $this->prefisso_flusso .
        if (empty($result[$meccanografico]['errors_stop']))
        {
            $result[$meccanografico]['correct'] = 'File elaborato correttamente';

            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/" . $this->cartella ."*" . $this->flusso . "MAST". $this->versione . "*");

            // Creo il file Esiti
            $filename_esiti = $prefisso_file . "001TB_ALU_ESIFINPRI.TXT";

            foreach ($datiFinaliEsiti as $riga_esito)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/" . $this->cartella ."/" . $filename_esiti, print_r($riga_esito, true), FILE_APPEND);
            }

            // Creo il file Assenze
            $filename_assenze = $prefisso_file . "002TB_ALU_ASSENZE.TXT";

            file_put_contents("/var/www-source/mastercom/tmp_siis/" . $this->cartella ."/" . $filename_assenze, "", FILE_APPEND);
            foreach ($datiFinaliAssenze as $riga_assenza)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/" . $this->cartella ."/" . $filename_assenze, print_r($riga_assenza, true), FILE_APPEND);
            }

            // Creo il file Competenze
            $filename_competenze = $prefisso_file . "003TB_ALU_CERT_COMP.TXT";

            file_put_contents("/var/www-source/mastercom/tmp_siis/" . $this->cartella ."/" . $filename_competenze, "", FILE_APPEND);
            foreach ($datiFinalicompetenze as $riga_competenza)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/" . $this->cartella ."/" . $filename_competenze, print_r($riga_competenza, true), FILE_APPEND);
            }

            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/'. $this->cartella . $this->prefisso_flusso .'_' . explode('-',$meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/". $this->cartella ."##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile)
            {
                if ($zip->open($zipFile) === TRUE)
                {
                    $zip->addFile("/var/www-source/mastercom/tmp_siis/". $this->cartella ."/##". $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                            or die ("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/". $this->cartella ."/##*" . $this->flusso . "MAST". $this->versione . "*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));

        return $result[$meccanografico]['errors_stop'];

    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico)
    {
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        $scuola = $this->getScuola($meccanografico);

        if (!empty($scuola['errors']))
        {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Codice di Prenotazione',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
        }

        $datiStudente = $this->getStudenti($meccanografico, 'check');

        if (!empty($datiStudente['errors']))
        {
            if (!empty($datiStudente['errors']['nessun_dato_presente']))
            {
                foreach ($datiStudente['errors']['nessun_dato_presente'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Nessun dato presente',
                        'valore' => $value
                    ];
                }

                return $result[$meccanografico]['errors_stop'];
            }

            if (!empty($datiStudente['errors']['codice_sidi']))
            {
                foreach ($datiStudente['errors']['codice_sidi'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Codice Sidi',
                        'valore' => $value
                    ];
                }
            }
        }

        // Recupero i dati delle assenze degli studenti selezionati
        if (empty($datiStudente['errors']))
        {
            $datiAssenze = $this->getAssenze($datiStudente, 'check');
            // Controlli dati assenze
            if (!empty($datiAssenze['errors']))
            {
                foreach ($datiAssenze['errors']['codice_sidi'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Assenze',
                        'valore' => $value
                    ];
                }
            }
        }

        if (!empty($result[$meccanografico]['errors_stop']))
        {
            return $result[$meccanografico]['errors_stop'];
        }
        else
        {
            return [];
        }
    }

    /**
     * Genera il file
     * @param type $meccanografico
     * @return type
     */
    public function generate($meccanografico)
    {
        return $this->esitiPrimaria($meccanografico);
    }
}