<?php

class SiisEsitiFinali extends Siis
{

    protected $db;
    protected $pdf;
    public $flusso;
    public $versione;

    function __construct()
    {
        $db = new MT\Mastercom\Db();
        $this->db = $db;

        $pdf = new PDFClass();
        $this->pdf = $pdf;

        $this->flusso = "00EF";
        $this->versione = "20180525";
    }

    function decode($field)
    {
        if (!is_array($field) && strlen($field) > 0)
        {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        }
        else
        {
            return $field;
        }
    }

    public function normalize_string($string)
    {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Estrae il dato riferito al parametro PERIODO_PAGELLA_IN_USO
     *
     * @return string
     */
    public function getPeriodoAttivo()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'PERIODO_PAGELLA_IN_USO'";
        return $this->db->query($query);
    }

    /**
     * Estrae il dato riferito al parametro PRIMO_PERIODO_SCOLASTICO
     *
     * @return int
     */
    public function getFrazioneTemporale()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'PRIMO_PERIODO_SCOLASTICO'";
        $arrFT = $this->db->query($query);

        $periodoAttivo = $this->getPeriodoAttivo();

        switch ($arrFT['valore'])
        {
            case 'quadrimestre':
                if (in_array((int) $periodoAttivo['valore'], [7,27]))
                {
                    $frazioneTemp = 4;
                }
                else
                {
                    $frazioneTemp = 5;
                }
                break;
            case 'pentamestre':
                $frazioneTemp = 7;
                break;
            case 'trimestre':
                $frazioneTemp = 3;
                break;
        }

        return $frazioneTemp;
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     */
    public function createZip($fileName, $codMecc, $timestamp = null)
    {

        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;

        exec("rm /var/www-source/mastercom/tmp_siis/esiti_finali/ESITI_FINALI_{$codMecc}_{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/esiti_finali/{$fileName}*.TXT");

        foreach ($glob as $file)
        {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/esiti_finali/ESITI_FINALI_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE)
            {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/esiti_finali/*.TXT");
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $codMecc
     */
    public function mergeFile($codMecc)
    {
        $codMecc = explode('-',$codMecc)[1];
        $timestamp = date('Ymd');

        $annoScolastico = $this->getAnnoScolastico();
        $glob = glob("/var/www-source/mastercom/tmp_siis/esiti_finali/ESITI_FINALI_{$codMecc}_{$timestamp}*.zip");

        foreach ($glob as $key => $file)
        {
            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/esiti_finali/{$basename}";

            if ($zip->open($nomeZip) == TRUE)
            {
                if ($key == 1)
                {
                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/esiti_finali/');
                    $zip->close();

                    $txt1 = glob("/var/www-source/mastercom/tmp_siis/esiti_finali/*{$codMecc}*.txt");

                    foreach ($txt1 as $value)
                    {
                        $basename1 = basename($value);
                        exec("mv /var/www-source/mastercom/tmp_siis/esiti_finali/{$basename1} /var/www-source/mastercom/tmp_siis/esiti_finali/{$basename1}_1.txt");
                    }
                }
                else
                {
                    exec("mkdir /var/www-source/mastercom/tmp_siis/esiti_finali/tmp/");

                    $zip->extractTo('/var/www-source/mastercom/tmp_siis/esiti_finali/tmp/');
                    $zip->close();
                }

            }
            else
            {
                echo 'Merge files failed';
            }
        }

        $txt = glob("/var/www-source/mastercom/tmp_siis/esiti_finali/tmp/*{$codMecc}*.txt");

        foreach ($txt as $value)
        {
            $basename = basename($value);
            exec("mv /var/www-source/mastercom/tmp_siis/esiti_finali/tmp/{$basename} /var/www-source/mastercom/tmp_siis/esiti_finali/tmp/{$basename}_2.txt");
            exec("mv /var/www-source/mastercom/tmp_siis/esiti_finali/tmp/{$basename}_2.txt /var/www-source/mastercom/tmp_siis/esiti_finali/");
        }

        $txt2 = glob("/var/www-source/mastercom/tmp_siis/esiti_finali/*{$codMecc}*.txt");

        $finalName = $codMecc.explode('/', $annoScolastico['valore'])[0].substr(explode('/', $annoScolastico['valore'])[1], 2, 2)  . $this->flusso . "MAST". $this->versione;

        foreach ($txt2 as $value)
        {
            $file = file_get_contents($value);
            file_put_contents("/var/www-source/mastercom/tmp_siis/esiti_finali/{$finalName}.txt", print_r($file, true), FILE_APPEND);
        }

        exec("/var/www-source/mastercom/tmp_siis/esiti_finali/*{$codMecc}*.txt_*.txt");
        exec("mv /var/www-source/mastercom/tmp_siis/esiti_finali/ESITI_FINALI_{$codMecc}_{$timestamp}*.zip /var/www-source/mastercom/tmp_siis/esiti_finali/tmp/");

        $this->createZip($finalName, $codMecc);
    }

    /**
     * Restituisce i dati della scuola in questione
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico)
    {
        $errors = ['errors' => ''];
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT scuole.id_scuola,
                            scuole.descrizione,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            scuole.codice_prenotazione,
                            sedi.id_sede,
                            sedi.descrizione as sedi_descrizione
                        FROM scuole,
                            sedi,
                            indirizzi
                        WHERE scuole.id_scuola = sedi.id_scuola::int
                            AND sedi.id_sede = indirizzi.id_sede::bigint
                            AND indirizzi.id_codice_ministeriale NOT IN (
                                SELECT id_indirizzo FROM indirizzi_ministeriali
                                    WHERE classificazione IN ('AA', 'EE')
                            )
                            AND indirizzi.tipo_indirizzo NOT IN ('6','7')
                            AND indirizzi.flag_canc = 0
                            AND scuole.flag_canc = 0
                            AND sedi.flag_canc = 0
                            AND codice_meccanografico = '{$mecc[0]}'
                            AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $datiScuola =  $this->db->query($query);

        if (array_key_exists(0, $datiScuola))
        {
            foreach ($datiScuola as $value)
            {
                if ($value['codice_prenotazione'] == '')
                {
                    $errors['errors']['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$value['codice_meccanografico']}-{$value['codice_meccanografico_secondario']}";

                    if (!empty($errors['errors']))
                    {
                        $datiScuola['errors'] = $errors['errors'];
                    }
                }
            }
        }
        else
        {
            if ($datiScuola['codice_prenotazione'] == '')
            {
                $errors['errors']['codice_prenotazione'] = "Errore: manca il codice prenotazione per la scuola {$datiScuola['codice_meccanografico']}-{$datiScuola['codice_meccanografico_secondario']}";

                if (!empty($errors['errors']))
                {
                    $datiScuola['errors'] = $errors['errors'];
                }
            }
        }

        return $datiScuola;
    }

    /**
     * Verifica se lo studente ha il codice SIDI settato
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student)
    {
        $errors = ['errors' => ''];

        if ($student['codice_alunno_ministeriale'] == '')
        {
            $errors['errors'] = "Errore: manca il codice SIDI per {$student['cognome']} {$student['nome']} classe {$student['classe']}{$student['sezione']}. "
                . "<br> Per scaricare il codice SIDI andare in Setup C06 - ESTRAZIONE ALUNNI SOGEI, inserire le credenziali, selezionare l'istituto e premere su Procedere";
        }

        return $errors;
    }

    /**
     * Estrae i voti di ogni studente
     *
     * @param array $studente
     * @return mixed
     */
    public function getVoti($studente)
    {
        $query = "SELECT DISTINCT descrizione,
                        materie.id_materia,
                        codice,
                        materie.codice_ministeriale,
                        tipo_materia,
                        voto_pagellina,
                        tipo_recupero,
                        esito_recupero,
                        ore_assenza,
                        monteore_totale
                    FROM voti_pagelline
                        INNER JOIN pagelline
                            ON voti_pagelline.id_pagellina = pagelline.id_pagellina
                        INNER JOIN materie
                            ON materie.id_materia = voti_pagelline.id_materia
                        INNER JOIN classi_prof_materie
                            ON materie.id_materia = classi_prof_materie.id_materia
                        INNER JOIN studenti
                            ON pagelline.id_studente = studenti.id_studente
                    WHERE pagelline.periodo = '{$studente['periodo']}'
                        AND pagelline.id_studente = {$studente['id_studente']}
                        AND classi_prof_materie.id_classe IN (
                            SELECT id_classe
                                FROM studenti_completi
                                WHERE id_studente = {$studente['id_studente']}
                        )
                        AND voti_pagelline.flag_canc = 0
                        AND pagelline.flag_canc = 0
                        AND materie.flag_canc = 0
                        AND voti_pagelline.id_materia IN (
                            SELECT id_materia
                                FROM classi_prof_materie
                                WHERE id_classe IN (
                                SELECT id_classe
                                    FROM studenti_completi
                                    WHERE id_studente = {$studente['id_studente']}
                            )
                        )
                        AND in_media_pagelle = 'SI'
                        AND tipo_materia != 'RELIGIONE'
                        AND tipo_materia != 'ALTERNATIVA'
                    ORDER BY materie.id_materia, descrizione";

        $voti = $this->db->query($query);

        return $voti;
    }

    /**
     * Estrae il dato riferito al parametro TIPO_VISUALIZZAZIONE_MINUTI_ASSENZA
     *
     * @return int
     */
    public function getPeriodi()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'TIPO_VISUALIZZAZIONE_MINUTI_ASSENZA'";
        return $this->db->query($query);
    }


    /**
     * Estrae il dato riferito al parametro SCALA_VOTI
     *
     * @return int
     */
    public function getScalaVoti()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'SCALA_VOTI'";
        $arrScalaVoti = $this->db->query($query);
        return $arrScalaVoti['valore'];
    }

    /**
     * Estrae l'elenco degli studenti per la scuola in questione con i seguenti
     * filtri:
     *
     * codice_ministeriale <> 'FAKE' (Indirizzo non FAKE)
     * classi 1,2,3 per indirizzi Q4
     * classi 1,2 per indirizzo Q3
     * esito <> Trasferito e Ritirato
     *
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null)
    {
        $errors = ['errors' => ''];
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            sc.sezione_sidi,
                            sc.descrizione_indirizzi,
                            codice_ministeriale,
                            tipo_indirizzo,
                            giudizio_ammissione_quinta,
                            ammesso_esame_quinta,
                            ammesso_esame_qualifica,
                            voto_qualifica,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            handicap,
                            grado_handicap,
                            tipo_handicap,
                            esito_corrente_calcolato,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            (
                                CASE WHEN tipo_indirizzo <> '1'
                                THEN sc.classe::int
                                ELSE
                                    CASE WHEN tipo_indirizzo = '1'
                                        THEN
                                            CASE
                                                WHEN sc.classe = '1' THEN 3
                                                WHEN sc.classe = '2' THEN 4
                                                WHEN sc.classe = '3' THEN 5
                                                WHEN sc.classe = '4' THEN 1
                                                WHEN sc.classe = '5' THEN 2
                                            END
                                    END
                                END
                            ) as anno_cron,
                            (
                                CASE WHEN tipo_indirizzo = '4'
                                    THEN '29'
                                    ELSE '9'
                                END
                            ) as periodo
                        FROM studenti_completi sc
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND sc.classe IN ('1','2')
                            AND sc.classificazione_indirizzi = 'Q3'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'

                UNION

                SELECT DISTINCT sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            sc.sezione_sidi,
                            sc.descrizione_indirizzi,
                            codice_ministeriale,
                            tipo_indirizzo,
                            giudizio_ammissione_quinta,
                            ammesso_esame_quinta,
                            ammesso_esame_qualifica,
                            voto_qualifica,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            handicap,
                            grado_handicap,
                            tipo_handicap,
                            esito_corrente_calcolato,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            (
                                CASE WHEN tipo_indirizzo <> '1'
                                THEN sc.classe::int
                                ELSE
                                    CASE WHEN tipo_indirizzo = '1'
                                        THEN
                                            CASE
                                                WHEN sc.classe = '1' THEN 3
                                                WHEN sc.classe = '2' THEN 4
                                                WHEN sc.classe = '3' THEN 5
                                                WHEN sc.classe = '4' THEN 1
                                                WHEN sc.classe = '5' THEN 2
                                            END
                                    END
                                END
                            ) as anno_cron,
                            (
                                CASE WHEN tipo_indirizzo = '4'
                                    THEN '29'
                                    ELSE '9'
                                END
                            ) as periodo
                        FROM studenti_completi sc
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND sc.classe IN ('1','2','3')
                            AND sc.classificazione_indirizzi = 'Q4'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'

                UNION

                SELECT DISTINCT sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            sc.sezione_sidi,
                            sc.descrizione_indirizzi,
                            codice_ministeriale,
                            tipo_indirizzo,
                            giudizio_ammissione_quinta,
                            ammesso_esame_quinta,
                            ammesso_esame_qualifica,
                            voto_qualifica,
                            crediti_terza,
                            crediti_quarta,
                            crediti_quinta,
                            crediti_reintegrati_terza,
                            crediti_reintegrati_quarta,
                            handicap,
                            grado_handicap,
                            tipo_handicap,
                            esito_corrente_calcolato,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            (
                                CASE WHEN tipo_indirizzo <> '1'
                                THEN sc.classe::int
                                ELSE
                                    CASE WHEN tipo_indirizzo = '1'
                                        THEN
                                            CASE
                                                WHEN sc.classe = '1' THEN 3
                                                WHEN sc.classe = '2' THEN 4
                                                WHEN sc.classe = '3' THEN 5
                                                WHEN sc.classe = '4' THEN 1
                                                WHEN sc.classe = '5' THEN 2
                                            END
                                    END
                                END
                            ) as anno_cron,
                            (
                                CASE WHEN tipo_indirizzo = '4'
                                    THEN '29'
                                    ELSE '9'
                                END
                            ) as periodo
                        FROM studenti_completi sc
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND sc.classe IN ('1','2','3','4')
                            AND sc.classificazione_indirizzi = 'SE'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'
                        ORDER BY codice_ministeriale, classe, sezione, cognome, nome
                        ";

        $arrAlunno = $this->db->query($query);

        if ($check)
        {
            if (is_array($arrAlunno))
            {
                foreach ($arrAlunno as $alunno)
                {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors']))
                    {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
            else
            {
                $errors['errors']['nessun_dato_presente'][] = "Non sono presenti dati da elaborare per la scuola {$mecc[0]}-{$mecc[1]}";
            }
        }

        if (!empty($errors['errors']))
        {
            return $errors;
        }
        else
        {
            return $arrAlunno;
        }
    }

    /**
     * Recupera le assenze degli studenti selezionati
     *
     * @param type $dati_studente
     * @param type $check
     * @return type
     */
    public function getAssenze($dati_studente, $check = null)
    {

        $annoScolastico = $this->getAnnoScolastico();

        $datiAssenze = [];
        // Ciclo gli studenti e tiro fuori le loro assenze
        if (!empty($dati_studente))
        {
            foreach ($dati_studente as $studente)
            {
                $id_studente = $studente['id_studente'];

                $query_assenze = "SELECT id_studente,
                                        EXTRACT (year FROM to_timestamp(data)) AS anno,
                                        EXTRACT (month FROM to_timestamp(data)) AS mese,
                                        tipo_assenza,
                                        count(*) as totale
                                    FROM assenze
                                    WHERE data <= " . time() ."
                                        AND id_studente = {$id_studente}
                                    GROUP BY
                                        id_studente,
                                        anno,
                                        mese,
                                        tipo_assenza
                                    ORDER BY id_studente,
                                        anno,
                                        mese,
                                        tipo_assenza
                                    ";

                $arrAssenze = $this->db->query($query_assenze, true);

                foreach($arrAssenze as $assenza)
                {
                    switch ($assenza['tipo_assenza'])
                    {
                        // assenze
                        case 1:
                            $gruppo_assenze = 'A';
                            break;

                        // entrate + assenza mattino
                        case 2:
                        case 6:
                        case 8:
                        case 12:
                            $gruppo_assenze = 'R';
                            break;

                        // uscite +  assenza pomeriggio
                        case 3:
                        case 7:
                        case 9:
                        case 18:
                            $gruppo_assenze = 'U';
                            break;

                        default:
                            $gruppo_assenze = 'X';
                            break;
                    }

                    $valore_anno_scolastico = explode('/', $annoScolastico['valore'])[0] . substr(explode('/', $annoScolastico['valore'])[1], 2, 2);
                    $chiave_assenza = $studente['codice_alunno_ministeriale'] . "-" . $assenza['anno'] . $assenza['mese'] . $gruppo_assenze;

                    $datiAssenze[$chiave_assenza]['codice_ministeriale_scuola'] = $studente['codice_meccanografico_secondario'];
                    $datiAssenze[$chiave_assenza]['anno_scolastico'] = $valore_anno_scolastico;
                    $datiAssenze[$chiave_assenza]['codice_alunno_sidi'] = $studente['codice_alunno_ministeriale'];
                    $datiAssenze[$chiave_assenza]['mese'] = $this->formatId($assenza['mese'], 2);
                    $datiAssenze[$chiave_assenza]['tipo_rilevazione'] = $gruppo_assenze;
                    $datiAssenze[$chiave_assenza]['numero_giorni'] += $assenza['totale'];
                }
            }
        }
        else
        {

            $datiAssenze = [];
        }
        // verifico i dati
        if ($check == 'verify')
        {
            if (is_array($datiAssenze))
            {
                foreach ($datiAssenze as $id => $assenze)
                    {
                    $checkAssenza = $this->checkDatiAssenze($dati_studente[$assenze['codice_alunno_sidi']], $assenze);

                    if (!empty($checkAssenza['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkAssenza['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors']))
        {
            return $errors;
        }
        else
        {
            return $datiAssenze;
        }
    }

   /**
     * Verifica se l'assenza ha i dati corretti
     *
     * @param array $studente
     * @param array $assenze
     * @return string
     */
    public function checkDatiAssenze($studente, $assenze)
    {
        $errors = ['errors' => ''];

        if ($assenze['codice_ministeriale_scuola'] == "")
        {
            $errors['errors'] = "Errore: codice alunno SIDI mancante per lo studente {$studente['cognome_alunno']} {$studente['nome']}.";
        }

        // SIDI vuole come anno scolastico anno di inizio a 4 cifre, anno di fine a 2 cifre (es. 201718)
        if (strlen($assenze['anno_scolastico']) != 6 || !is_numeric($assenze['anno_scolastico']))
        {
            $errors['errors'] = "Errore: anno scolastico errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if ($assenze['codice_alunno_sidi'] == '' || !is_numeric($assenze['codice_alunno_sidi']))
        {
            $errors['errors'] = "Errore: codice SIDI errato o assente per lo studente {$studente['cognome_alunno']} {$studente['nome']} codice fiscale: {$studente['codice_fiscale']}"
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if (strlen($assenze['mese']) != 2 || !is_numeric($assenze['mese']))
        {
            $errors['errors'] = "Errore: mese errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if ($assenze['tipo_rilevazione'] == '' || strlen($assenze['tipo_rilevazione']) != 1)
        {
            $errors['errors'] = "Errore: codice tipo rilevazione errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        if (!is_numeric($assenze['numero_giorni'])) {
            $errors['errors'] = "Errore: totale mensile errato per {$studente['cognome_alunno']} {$studente['nome']} codice SIDI {$assenze['codice_alunno_sidi']}."
            . "<br>Anno {$assenze['anno_scolastico']}, mese {$assenze['mese']}, tipo {$assenze['tipo_rilevazione']}";
        }

        return $errors;
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4)
    {
        if (!isset($id) || trim($id) === '')
        {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++)
        {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    }

    /**
     * Restituisce l'ID ed il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola,
                        codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
    //}}} </editor-fold>
    }

    private function esiti($meccanografico)
    {
        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];
        $datiFlusso = [];

        $mecc = explode('-', $meccanografico);
        $annoScolastico = $this->getAnnoScolastico();
        $scalaVoti = $this->getScalaVoti();
        $datiStudente = $this->getStudenti($meccanografico);

        $assenze_da_pagella = 0;
        $monteore_da_pagella = 0;
        $almeno_un_voto = false;
        $tipi_recupero = true;
        $voti_insuff = false;
        $recupero = false;
        $esito_recupero = 'SI';

        foreach ($datiStudente as $studente)
        {
            $voti = $this->getVoti($studente);

            foreach ($voti as $voto)
            {
                if ($voto['monteore_totale'] > 0 && $voto['tipo_materia'] != 'CONDOTTA')
                {
                    $assenze_da_pagella += $voto['ore_assenza'];
                    $monteore_da_pagella += $voto['monteore_totale'];
                }

                if ($voto['voto_pagellina'] != '')
                {
                    $almeno_un_voto = true;
                    if ($scalaVoti == 'CENTINAIA' && is_numeric($voto['voto_pagellina']))
                    {
                        $voto['voto_pagellina'] = $voto['voto_pagellina'] / 10;
                    }

                    if (
                        !empty($voto['voto_pagellina'])
                            && (floatval($voto['voto_pagellina']) < 6 || !is_numeric($voto['voto_pagellina']))
                    )
                    {
                        if ($voto['tipo_recupero'] == '' || $voto['esito_recupero'] != 'SI')
                        {
                            if ($voto['tipo_recupero'] == '')
                            {
                                $tipi_recupero = false;
                            }
                            else
                            {
                                $recupero = true;
                            }

                            if ($voto['esito_recupero'] == '')
                            {
                                $esito_recupero = '';
                            }
                            elseif ($voto['esito_recupero'] != 'SI' && $esito_recupero == 'SI')
                            {
                                $esito_recupero = 'NO';
                            }
                            $voti_insuff = true;
                        }
                    }
                    else
                    {
                        if ($voto['esito_recupero'] != '')
                        {
                            $recupero = true;
                        }
                    }
                }
            }


            if ($monteore_da_pagella > 0)
            {
                $percentuale_totmin = round($assenze_da_pagella / $monteore_da_pagella * 100);
            }
            else
            {
                $percentuale_totmin = 0;
            }

            if ($percentuale_totmin >= 25)
            {
                if ($almeno_un_voto)
                {
                    //L’alunno non ha frequentato per almeno tre quarti dell’orario annuale, ma ha usufruito della deroga
                    $validita_as = 1;
                }
                else
                {
                    //L’alunno non ha frequentato per almeno tre quarti dell’orario annuale
                    $validita_as = 2;
                }
            }
            else
            {
                //L’alunno ha frequentato per almeno tre quarti dell’orario annuale
                $validita_as = 0;
            }

            $esito_fine_anno = '';
			$esito_integrazione = '';

            if ($studente['tipo_indirizzo'] == 2 and $studente['anno_cron'] == 3)
            {
                if ($alunno['ammesso_esame_qualifica'] == 'SI' and $alunno['voto_qualifica'] >= 60)
                {
                    //QUALIFICATO
                    $esito_fine_anno = 38;
                }
                else
                {
                    //NON QUALIFICATO
                    $esito_fine_anno = 19;
                }
            }
            elseif($studente['tipo_indirizzo'] == '2' && $studente['anno_cron'] == '1')
            {
                if ($almenoUnVoto)
                {
                    if (
                            (strtolower($studente['esito_corrente_calcolato']) == 'ammesso alla classe seconda con modifica del pfi')
                            ||
                            (strtolower($studente['esito_corrente_calcolato']) == 'ammesso alla classe seconda con revisione del pfi')
                        )
                    {
                        $esito_fine_anno = 59;
                    }
                    elseif (strtolower($studente['esito_corrente_calcolato']) == 'ammesso alla classe seconda')
                    {
                        $esito_fine_anno = 22;
                    }
                    else
                    {
                        $esito_fine_anno = 27;
                    }
                }
            }
            else
            {
                if (!($voti_insuff))
                {
                    //Non ho nessun voto insufficiente
                    if ($recupero && $esito_recupero == 'SI')
                    {
                        //Ho dei recuperi con esito positivo
                        $esito_fine_anno = 46;
                        $esito_integrazione = 22;
                    }
                    else
                    {
                        //Lo studente non ha fatto recuperi
                        $esito_fine_anno = 22;
                    }
                }
                elseif (!($tipi_recupero))
                {
                    //Ho delle insufficienze ma almeno una di esse non ha un tipo di recupero
                    $esito_fine_anno = 27;
                }
                elseif (in_array($esito_recupero, ['NO', 'ASSENTE', 'NI']))
                {
                    //Ho delle insufficenze, tutte con recupero compilato, e almeno un recupero negativo
                    $esito_fine_anno = 46;
                    $esito_integrazione = 27;
                }
                elseif ($esito_recupero == '')
                {
                    //Ho delle insufficenze, tutte con recupero, ma di cui almeno uno non compilato
                    $esito_fine_anno = 46;
                }
                elseif ($esito_recupero == 'SI')
                {
                    //Ho delle insufficenze, tutte con recupero compilato, e tutti i recuperi positivi (non dovrei mai cadere in questo caso)
                    $esito_fine_anno = 46;
                    $esito_integrazione = 22;
                }
            }

            if (strpos($studente['esito_corrente_calcolato'], 'Non ammesso') === 0)
            {
                //NON AMMESSO/A
                $esito_fine_anno = 27;
            }
            elseif (strpos($studente['esito_corrente_calcolato'], 'Ammesso') === 0)
            {
                //AMMESSO/A
                $esito_fine_anno = 22;
            }
            else
            {
                //SOSPENSIONE DAL GIUDIZIO
                $esito_fine_anno = 46;
            }

            if ($esito_fine_anno != '')
            {
                $chiaveDatiFlusso = $studente['classe'] . ' ' . $studente['sezione'] . ' ' . $studente['cognome'] . ' ' . $studente['nome'] . ' - ' . $studente['codice_fiscale'];

                $datiFlusso[$chiaveDatiFlusso][] = [
                    explode('-', $meccanografico)[1] . "|",
                    explode('/', $annoScolastico['valore'])[0] . "|",
                    $studente['codice_alunno_ministeriale'] . "|",
                    $studente['anno_cron'] . "|",
                    $validita_as . "|",
                    $esito_fine_anno . "|",
                    $esito_integrazione . "|",
                ];
            }
        }

        $datiFinali = array_values($datiFlusso);

        $datiAssenze = $this->getAssenze($datiStudente);
        foreach ($datiAssenze as $id_assenza => $assenze)
        {
            $dati_finali_assenze[$id_assenza] = implode("|", $assenze) . "|\n";
        }
        $datiFinaliAssenze = array_values($dati_finali_assenze);


        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $timestamp = date('YmdHi');

        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolastico['valore'])[0] .
                        $this->flusso .
                        "MAST" .
                        $this->versione .
                        $timestamp;

        // Genero i file necessari
        if (empty($result[$meccanografico]['errors_stop']))
        {
            $result[$meccanografico]['correct'] = 'File elaborato correttamente';

            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/esiti_finali/*" . $this->flusso . "MAST". $this->versione . "*");


            $timestamp = date('YmdHi');

            // genero il file scrutini
            $filename_esiti = $prefisso_file . "001TB_ALU_ESI_FIN.TXT";

            foreach ($datiFinali as $righe)
            {
                foreach ($righe as $riga)
                {
                    foreach ($riga as $key => $value)
                    {
                        if ($key==6)
                        {
                            $value .= "\n";
                        }

                        file_put_contents("/var/www-source/mastercom/tmp_siis/esiti_finali/" . $filename_esiti, print_r($value, true), FILE_APPEND);
                    }
                }
            }

            // Creo il file Assenze
            $filename_assenze = $prefisso_file . "002TB_ALU_ASSENZE.TXT";

            foreach ($datiFinaliAssenze as $riga_assenza)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/esiti_finali/" . $filename_assenze, print_r($riga_assenza, true), FILE_APPEND);
            }

            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/esiti_finali/ESITI_FINALI_' . explode('-',$meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/esiti_finali/##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile)
            {
                if ($zip->open($zipFile) === TRUE)
                {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/esiti_finali/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                            or die ("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/esiti_finali/##*" . $this->flusso . "MAST". $this->versione . "*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));

        return $result[$meccanografico]['errors_stop'];
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico)
    {
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        $scuola = $this->getScuola($meccanografico);

        if (!empty($scuola['errors']))
        {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Codice di Prenotazione',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
        }

        $datiStudente = $this->getStudenti($meccanografico, 'check');

        if (!empty($datiStudente['errors']))
        {
            if (!empty($datiStudente['errors']['nessun_dato_presente']))
            {
                foreach ($datiStudente['errors']['nessun_dato_presente'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Nessun dato presente',
                        'valore' => $value
                    ];
                }

                return $result[$meccanografico]['errors_stop'];
            }

            if (!empty($datiStudente['errors']['codice_sidi']))
            {
                foreach ($datiStudente['errors']['codice_sidi'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Codice Sidi',
                        'valore' => $value
                    ];
                }
            }
        }

        // Recupero i dati delle assenze degli studenti selezionati
        $datiAssenze = $this->getAssenze($datiStudente, 'check');

        // Controlli dati assenze
        if (!empty($datiAssenze['errors']))
        {
            foreach ($datiAssenze['errors']['codice_sidi'] as $value)
            {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Assenze',
                    'valore' => $value
                ];
            }
        }

        if (!empty($result[$meccanografico]['errors_stop']))
        {
            return $result[$meccanografico]['errors_stop'];
        }
        else
        {
            return [];
        }
    }

    /**
     * Genera il file
     * @param type $meccanografico
     * @return type
     */
    public function generate($meccanografico)
    {
        return $this->esiti($meccanografico);
    }
}