<?php


/*

 * tipo percorso

    id_tipo_percorso, descrizione_tipo_percorso
    1, Istruzione liceale
    2, Istruzione Tecnica
    3, Istruzione professionale
    7, Istruzione e formazione professionale regionale
 *
 *
 *
 * */
/*
tipo settore

    id_tipo_settore, descrizione_tipo_settore, id_tipo_percorso
    53,-,1
    17,Settore economico,2
    18,Settore tecnologico,2
    27,-,3
    30,"Cultura, informazione e tecnologie informatiche",7
    31,Turismo e sport,7
    32,<PERSON><PERSON>zi commerciali,7
    33,<PERSON><PERSON><PERSON> alla persona,7
    34,Manifattura e artigianato,7
    35,Automazione industriale,7
    36,Meccanica, impianti e costruzioni,7
    37,Agro - alimentare,7

 *  */

class SiisConsiglioOrientativo extends Siis {

    protected $db;
    protected $pdf;

    function __construct() {
        $db = new MT\Mastercom\Db();
        $this->db = $db;

        $pdf = new PDFClass();
        $this->pdf = $pdf;
    }

    function decode($field) {
        if (!is_array($field) && strlen($field) > 0) {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        } else {
            return $field;
        }
    }

    public function normalize_string($string) {
        //remove pipe character
        $string = str_replace('|', '', $string);

        //remove newlines and multiple whitespaces
        $string = preg_replace('/\s+/', ' ', $string);

        $string = $this->decode($string);
        return $string;
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico() {
        $query = "SELECT valore FROM parametri WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     * $filename riporta il prefisso dei tre file dell'anagrafe nazionale
     *
     * @return int
     */
    public function createZip($fileName, $codMecc, $timestamp = null) {

        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;

        exec("rm /var/www-source/mastercom/tmp_siis/consiglio_orientativo/CONSIGLIOORIENTATIVO_{$codMecc}_{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/{$fileName}*.TXT");

        foreach ($glob as $file) {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/consiglio_orientativo/CONSIGLIOORIENTATIVO_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE) {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/consiglio_orientativo/*.TXT");
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $cod
     */
    public function mergeFile($cod) {

        $annoScolatico = $this->getAnnoScolastico();
        $timestamp = date('YmdHi');
        $timestamp2 = date('Ymd');
        $codMecc = explode('-', $cod)[1];

        $path_files = "/var/www-source/mastercom/tmp_siis/consiglio_orientativo/";

        // creo i file finali
        $codici_scuola = $this->getCodiciScuola($cod);
        $prefisso_file = $codMecc .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolatico['valore'])[0] .
                        "00CO" .
                        "MAST" .
                        "20160930" .
                        $timestamp;

        $glob = glob($path_files . "CONSIGLIOORIENTATIVO_{$codMecc}_{$timestamp2}*.zip");

        // recupero i due file: quello creato in locale e quello creato sull'altro server
        foreach ($glob as $key => $file) {

            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = $path_files . $basename;

            if ($zip->open($nomeZip) == TRUE) {

                exec("mkdir {$path_files}tmp/{$key}/");

                $zip->extractTo("{$path_files}tmp/{$key}/");
                $zip->close();

                $glob_zip = glob("{$path_files}tmp/{$key}/*");

                foreach ($glob_zip as $file_singolo)
                {
                    if (strpos($file_singolo, "001TB_ALU_CONSORIE.TXT") !== false)
                    {
                        // scuole (sovrascrivo in quanto la scuola è una sola)
                        $file_scuole = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}001TB_ALU_CONSORIE.TXT", print_r($file_scuole, true));
                    }
                }
                exec("rm {$path_files}tmp/{$key}/*");

            }
            else
            {
                echo 'Merge files failed';
            }
        }

        // creo lo zip finale
        $this->createZip($prefisso_file, $codMecc, $timestamp);

        // creo il .dat
        file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/##" . $prefisso_file . ".dat", "");

        $fileZip = glob($path_files . 'CONSIGLIOORIENTATIVO_' . $codMecc . '_' . $timestamp . '.zip');
        $zip = new ZipArchive;

        foreach ($fileZip as $zipFile) {
            if ($zip->open($zipFile) === TRUE) {
                $zip->addFile('/var/www-source/mastercom/tmp_siis/consiglio_orientativo/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                        or die ("ERROR: Could not add file: {$prefisso_file}");
                $zip->close();
            }
        }
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico) {
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];

        // Alunno
        $datiStudente = $this->getStudenti($meccanografico, 'verify');
        if (!empty($datiStudente['errors'])) {
            foreach ($datiStudente['errors']['codice_sidi'] as $value) {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc'   => 'Studenti',
                    'valore' => $value
                ];
            }
        }


        if (!empty($result[$meccanografico]['errors_stop'])) {
            return $result[$meccanografico]['errors_stop'];
        } else {
            return [];
        }
    }

    public function generate($meccanografico) {
        return $this->consiglio_orientativo($meccanografico);
    }

   /**
     * Restituisce i dati della scuola in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = [];
        $datiScuola = [];

        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT
                        sc.id_scuola,
                        sc.codice_fiscale,
                        sc.codice_meccanografico,
                        sc.codice_meccanografico_secondario,
                        sc.descrizione,
                        sc.codice_fiscale,
                        sc.persona_giuridica,
                        sc.tipo_istituto_abbreviato,
                        sc.sito_scuola,
                        sc.decreto_ministeriale,
                        sc.id_tipo_scuola,
                        sc.istituto_dichiarante,
                        sc.codice_prenotazione,

                        sedi.id_comune,
                        sedi.email1,
                        sedi.email2,
                        sedi.cap,
                        sedi.indirizzo,
                        sedi.id_sede,
                        sedi.descrizione as sedi_descrizione,
                        sedi.cap,
                        sedi.telefono,
                        sedi.telefono2,

                        CASE
                            WHEN indirizzi_ministeriali.classificazione IN ('EE', 'AA', 'MM') THEN indirizzi_ministeriali.classificazione
                            ELSE 'SS'
                        END AS ordine,

                        ts.descrizione as descr_tipo

                    FROM scuole sc,
                        sedi,
                        indirizzi,
                        indirizzi_ministeriali,
                        tipi_scuole ts

                    WHERE sc.id_scuola = sedi.id_scuola::int
                        AND ts.codice = sc.id_tipo_scuola
                        AND sedi.id_sede = indirizzi.id_sede::bigint
                        AND indirizzi.id_codice_ministeriale = indirizzi_ministeriali.id_indirizzo
                        AND indirizzi_ministeriali.classificazione NOT IN ('AA')
                        AND indirizzi.tipo_indirizzo NOT IN ('7')
                        AND indirizzi.flag_canc = 0
                        AND sc.flag_canc = 0
                        AND sedi.flag_canc = 0
                        AND ts.flag_canc = 0
                        AND indirizzi_ministeriali.flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'
                        LIMIT 1";

        $arrScuola =  $this->db->query($query);

        // verifica dati
        if (!empty($arrScuola))
        {
            if (strlen(str_replace("&#039;" ,"",$arrScuola['tipo_istituto_abbreviato'])) > 16) {
                $errors['codice_prenotazione'][] = "Errore: Il tipo scuola è troppo lungo per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}.<br>La lunghezza massima consentita è di 16 caratteri.";
            }
            if ($arrScuola['codice_prenotazione'] == '') {
                $errors['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
            if ($arrScuola['id_comune'] == '') {
                $errors['codice_prenotazione'][] = "Errore: manca il codice del comune per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
            if (strlen($arrScuola['codice_fiscale']) > 16) {
                $errors['codice_prenotazione'][] = "Errore: codice fiscale errato per la scuola {$arrScuola['codice_meccanografico']}-{$arrScuola['codice_meccanografico_secondario']}";
            }
        }
        else
        {
            $errors['codice_prenotazione'][] = "Dati Scuola mancanti per la scuola {$mecc[0]} - {$mecc[1]}";
        }

        $datiScuola[$arrScuola["id_scuola"]] = [
            'codice_cap'								=>	$arrScuola['cap'],
            'codice_fiscale_scuola'						=>	$arrScuola['codice_fiscale'],
            'codice_meccanografico_forte_scuola'		=>	$arrScuola['codice_meccanografico'],
            'codice_meccanografico_debole_scuola'		=>	$arrScuola['codice_meccanografico_secondario'],
            'codice_comune'								=>	$arrScuola['id_comune'],
            'descrizione_indirizzo_posta_elettronica'	=>	$arrScuola['email1'],
            'secondo_indirizzo_posta_elettronica'		=>	$arrScuola['email2'],
            'riservato_al_gestore_01'					=>	"",
            'riservato_al_gestore_02'					=>	"",
            'riservato_al_gestore_03'					=>	"",
            'riservato_al_gestore_04'					=>	"",
            'riservato_al_gestore_05'					=>	"",
            'riservato_al_gestore_06'					=>	"",
            'identificativo_scuola' 					=>	$arrScuola['codice_prenotazione'] . $this->formatId($arrScuola['id_scuola']),
            'descrizione_indirizzo_scuola'				=>	$arrScuola['indirizzo'],
            'descrizione_nome_scuola'					=>	$arrScuola['nome'],
            'codice_scuola_utente'						=>	$arrScuola['ordine'],
            'flag_persona_giuridica'					=>	$arrScuola['persona_giuridica'],
            'descrizione_sigla_scuola'					=>	str_replace("&#039;" ,"",$arrScuola['tipo_istituto_abbreviato']),
            'descrizione_indirizzo_sito_internet'		=>	$arrScuola['sito_scuola'],
            'numero_telefonico'							=>	$arrScuola['telefono'],
            'secondo_numero_telefonico'					=>	$arrScuola['telefono2'],
            'descrizione_tipo_scuola'					=>	$arrScuola['descr_tipo'],
            'decreto_ministeriale'						=>	$arrScuola['decreto_ministeriale'],
            'tipo'										=>	$arrScuola['id_tipo_scuola'],
            'flag_istituto_dichiarante'					=>	$arrScuola['istituto_dichiarante'],

        ];

        if ($check == 'verify')
        {
            // aggiungo gli eventuali errori
            $datiScuola['errors'] = $errors;
        }

        return $datiScuola;
        //}}} </editor-fold>
    }

    /**
     * Restituisce i dati degli alunni in questione
     * Contiene l'array da utilizzare per l'invio
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getStudenti($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = [];
        $datiStudenti = [];

        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);

        $mecc = explode('-', $meccanografico);

        $query_p1 = "
            SELECT DISTINCT ON (id_studente)
                id_studente,
                cognome,
                nome,
                codice_alunno_ministeriale,
                data_consiglio_orientativo,
                id_tipo_percorso,
                descrizione_tipo_percorso,
                id_tipo_settore,
                descrizione_tipo_settore,
                id_indirizzo_settore,
                descrizione_indirizzo
            FROM
                studenti_completi,
                consiglio_orientativo_template
            WHERE
        ";

        $query_p2 = "
            AND codice_ministeriale != 'AA'
            AND classificazione_indirizzi NOT IN ('SE')
            AND esito_corrente_calcolato NOT LIKE 'Trasferit%'
            AND esito_corrente_calcolato NOT LIKE 'Ritirato%'
            AND id_codice_ministeriale NOT IN (94)
            AND descrizione_indirizzi NOT ILIKE '%RITIR%'
            AND descrizione_indirizzi NOT ILIKE '%TRASFE%'
            AND descrizione_indirizzi NOT ILIKE '%PREISCR%'
            AND codice_meccanografico = '{$mecc[0]}'
            AND codice_meccanografico_secondario = '{$mecc[1]}'
            AND classe != '-1'
            AND tipo_indirizzo = '4'
            AND classe = '3'
        ";

        $query = $query_p1
            . " studenti_completi.id_consiglio_orientativo = consiglio_orientativo_template.id_consiglio_orientativo_template
                AND studenti_completi.id_consiglio_orientativo IS NOT NULL "
            . $query_p2
            . " UNION ALL "
            . $query_p1
            . " studenti_completi.id_consiglio_orientativo2 = consiglio_orientativo_template.id_consiglio_orientativo_template
                AND studenti_completi.id_consiglio_orientativo2 IS NOT NULL "
            . $query_p2
            . " UNION ALL "
            . $query_p1
            . " studenti_completi.id_consiglio_orientativo3 = consiglio_orientativo_template.id_consiglio_orientativo_template
                AND studenti_completi.id_consiglio_orientativo3 IS NOT NULL "
            . $query_p2;


        // $query = "
        //     SELECT DISTINCT ON (id_studente)
        //         id_studente,
        //         cognome,
        //         nome,
        //         codice_alunno_ministeriale,
        //         data_consiglio_orientativo,
        //         id_tipo_percorso,
        //         descrizione_tipo_percorso,
        //         id_tipo_settore,
        //         descrizione_tipo_settore,
        //         id_indirizzo_settore,
        //         descrizione_indirizzo
        //     FROM
        //         studenti_completi,
        //         consiglio_orientativo_template
        //     WHERE
        //         (
        //             studenti_completi.id_consiglio_orientativo = consiglio_orientativo_template.id_consiglio_orientativo_template
        //             OR (studenti_completi.id_consiglio_orientativo IS NULL AND studenti_completi.id_consiglio_orientativo2 = consiglio_orientativo_template.id_consiglio_orientativo_template)
        //             OR (studenti_completi.id_consiglio_orientativo IS NULL AND studenti_completi.id_consiglio_orientativo2 IS NULL AND studenti_completi.id_consiglio_orientativo3 = consiglio_orientativo_template.id_consiglio_orientativo_template)
        //         )
        //         AND codice_ministeriale != 'AA'
        //         AND classificazione_indirizzi NOT IN ('SE')
        //         AND esito_corrente_calcolato NOT LIKE 'Trasferit%'
        //         AND esito_corrente_calcolato NOT LIKE 'Ritirato%'
        //         AND id_codice_ministeriale NOT IN (94)
        //         AND descrizione_indirizzi NOT ILIKE '%RITIR%'
        //         AND descrizione_indirizzi NOT ILIKE '%TRASFE%'
        //         AND descrizione_indirizzi NOT ILIKE '%PREISCR%'
        //         AND codice_meccanografico = '{$mecc[0]}'
        //         AND codice_meccanografico_secondario = '{$mecc[1]}'
        //         AND classe != '-1'
        //         AND tipo_indirizzo = '4'
        //         AND classe = '3'
        // ";

        $arrAlunno = $this->db->query($query, true);
                file_put_contents('/tmp/co', print_r($arrAlunno,true), FILE_APPEND);

        $i = [];
        foreach ($arrAlunno as $alunno)
        {
            $id_studente = $alunno['id_studente'];
            $i[$id_studente] = $i[$id_studente] + 1;

            if (in_array($alunno['id_tipo_percorso'], [7, 9, 10, 11, 12, 13]))
            {
                $alunno['id_tipo_settore'] = '';
                $alunno['id_indirizzo_settore'] = '';
            }
            if ($alunno['id_tipo_settore'] == 0)
            {
                $alunno['id_tipo_settore'] = '';
            }
            if ($alunno['id_indirizzo_settore'] == 0)
            {
                $alunno['id_indirizzo_settore'] = '';
            }

            if (isset($datiStudenti[$id_studente])){
                if ($alunno['id_tipo_settore'] != ''
                    //&& $alunno['id_indirizzo_settore'] != ''
                ){
                    $id_studente = $id_studente . '_' . $i[$id_studente];
                } else {
                    continue;
                }
            }

            $datiStudenti[$id_studente] = [
                "codice_debole_scuola"          => $mecc[1],
                "anno_scolastico"               => $anno_scolastico[0],
                "codice_alunno_ministeriale"    => $alunno['codice_alunno_ministeriale'],
                "progressivo_tipo_percorso"     => $alunno['id_tipo_percorso'],
                "progressivo_tipo_settore"      => $alunno['id_tipo_settore'],
                "progressivo_indirizzo_settore" => $alunno['id_indirizzo_settore'],
                "data_consiglio_orientativo"    => date('Y-m-d', $alunno['data_consiglio_orientativo']),

            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiStudenti)) {
                foreach ($datiStudenti as $id => $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiStudenti;
        }
	//}}} </editor-fold>
    }

    public function getAreaInteresse($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">

        $errors = [];
        $datiStudenti = [];

        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);

        $mecc = explode('-', $meccanografico);

        $query = "
            SELECT DISTINCT
                sc.id_studente,
                sc.cognome,
                sc.nome,
                sc.codice_alunno_ministeriale,
                coc.id_ministeriale as prog_area_int,
                coc.descrizione,
                coc.categoria,
                coc.tipologia,
                coa.testo,
                coa.chi_inserisce,
                coa.data_inserimento
            FROM
                consiglio_orientativo_caratteristiche coc
            INNER JOIN
                consiglio_orientativo_abbinamenti coa
            ON
                coc.id_caratteristica = coa.id_caratteristica
            INNER JOIN
                studenti_completi sc
            ON
                coa.id_studente = sc.id_studente
            WHERE
                coc.categoria = 'AREA_INTERESSI'
                AND coa.flag_canc = 0
                AND sc.codice_ministeriale != 'AA'
                AND sc.classificazione_indirizzi NOT IN ('SE')
                AND sc.esito_corrente_calcolato NOT LIKE 'Trasferit%'
                AND sc.esito_corrente_calcolato NOT LIKE 'Ritirato%'
                AND sc.id_codice_ministeriale NOT IN (94)
                AND sc.descrizione_indirizzi NOT ILIKE '%RITIR%'
                AND sc.descrizione_indirizzi NOT ILIKE '%TRASFE%'
                AND sc.descrizione_indirizzi NOT ILIKE '%PREISCR%'
                AND sc.codice_meccanografico = '{$mecc[0]}'
                AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                AND sc.classe != '-1'
                AND sc.tipo_indirizzo = '4'
                AND sc.classe = '3';";


        $arrAlunno = $this->db->query($query, true);
        file_put_contents('/tmp/co_AI', print_r($arrAlunno,true), FILE_APPEND);

        foreach ($arrAlunno as $alunno)
        {
            $id_studente = $alunno['id_studente'];
            $chiave = $id_studente . '_' . $alunno['prog_area_int'];
            $datiStudenti[$chiave] = [
                "codice_debole_scuola"          => $mecc[1],
                "anno_scolastico"               => $anno_scolastico[0],
                "codice_alunno_ministeriale"    => $alunno['codice_alunno_ministeriale'],
                "progressivo_area_interesse"    => $alunno['prog_area_int'],
            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiStudenti)) {
                foreach ($datiStudenti as $id => $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiStudenti;
        }
	//}}} </editor-fold>
    }

    public function getAmbito($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">

        $errors = [];
        $datiStudenti = [];

        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);

        $mecc = explode('-', $meccanografico);

        $query = "
            SELECT DISTINCT
                sc.id_studente,
                sc.cognome,
                sc.nome,
                sc.codice_alunno_ministeriale,
                coc.id_ministeriale as prog_ambito,
                coc.descrizione,
                coc.categoria,
                coc.tipologia,
                coa.testo,
                coa.chi_inserisce,
                coa.data_inserimento
            FROM
                consiglio_orientativo_caratteristiche coc
            INNER JOIN
                consiglio_orientativo_abbinamenti coa
            ON
                coc.id_caratteristica = coa.id_caratteristica
            INNER JOIN
                studenti_completi sc
            ON
                coa.id_studente = sc.id_studente
            WHERE
                coc.categoria = 'AMBITO'
                AND coa.flag_canc = 0
                AND sc.codice_ministeriale != 'AA'
                AND sc.classificazione_indirizzi NOT IN ('SE')
                AND sc.esito_corrente_calcolato NOT LIKE 'Trasferit%'
                AND sc.esito_corrente_calcolato NOT LIKE 'Ritirato%'
                AND sc.id_codice_ministeriale NOT IN (94)
                AND sc.descrizione_indirizzi NOT ILIKE '%RITIR%'
                AND sc.descrizione_indirizzi NOT ILIKE '%TRASFE%'
                AND sc.descrizione_indirizzi NOT ILIKE '%PREISCR%'
                AND sc.codice_meccanografico = '{$mecc[0]}'
                AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                AND sc.classe != '-1'
                AND sc.tipo_indirizzo = '4'
                AND sc.classe = '3';";


        $arrAlunno = $this->db->query($query, true);
        file_put_contents('/tmp/co_Ambito', print_r($arrAlunno,true), FILE_APPEND);

        foreach ($arrAlunno as $alunno)
        {
            $id_studente = $alunno['id_studente'];
            $chiave = $id_studente . '_' . $alunno['prog_ambito'];
            $datiStudenti[$chiave] = [
                "codice_debole_scuola"          => $mecc[1],
                "anno_scolastico"               => $anno_scolastico[0],
                "codice_alunno_ministeriale"    => $alunno['codice_alunno_ministeriale'],
                "progressivo_ambito"            => $alunno['prog_ambito'],
                "descrizione_altre_attivita"    => $alunno['testo'],
            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiStudenti)) {
                foreach ($datiStudenti as $id => $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiStudenti;
        }
	//}}} </editor-fold>
    }

    public function getCertificazioni($meccanografico, $check = null) {
    //{{{ <editor-fold defaultstate="collapsed">

        $errors = [];
        $datiStudenti = [];

        $anno_scolastico = explode("/", $this->getAnnoScolastico()['valore']);

        $mecc = explode('-', $meccanografico);

        $query = "
            SELECT DISTINCT
                sc.id_studente,
                sc.cognome,
                sc.nome,
                sc.codice_alunno_ministeriale,
                coc.id_ministeriale as prog_cert,
                coc.descrizione,
                coc.categoria,
                coc.tipologia,
                coa.testo,
                coa.chi_inserisce,
                coa.data_inserimento
            FROM
                consiglio_orientativo_caratteristiche coc
            INNER JOIN
                consiglio_orientativo_abbinamenti coa
            ON
                coc.id_caratteristica = coa.id_caratteristica
            INNER JOIN
                studenti_completi sc
            ON
                coa.id_studente = sc.id_studente
            WHERE
                coc.categoria = 'CERTIFICAZIONI'
                AND coa.flag_canc = 0
                AND sc.codice_ministeriale != 'AA'
                AND sc.classificazione_indirizzi NOT IN ('SE')
                AND sc.esito_corrente_calcolato NOT LIKE 'Trasferit%'
                AND sc.esito_corrente_calcolato NOT LIKE 'Ritirato%'
                AND sc.id_codice_ministeriale NOT IN (94)
                AND sc.descrizione_indirizzi NOT ILIKE '%RITIR%'
                AND sc.descrizione_indirizzi NOT ILIKE '%TRASFE%'
                AND sc.descrizione_indirizzi NOT ILIKE '%PREISCR%'
                AND sc.codice_meccanografico = '{$mecc[0]}'
                AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                AND sc.classe != '-1'
                AND sc.tipo_indirizzo = '4'
                AND sc.classe = '3';";


        $arrAlunno = $this->db->query($query, true);
        file_put_contents('/tmp/co_certificazioni', print_r($arrAlunno,true), FILE_APPEND);

        foreach ($arrAlunno as $alunno)
        {
            $id_studente = $alunno['id_studente'];
            $chiave = $id_studente . '_' . $alunno['prog_cert'];
            $datiStudenti[$chiave] = [
                "codice_debole_scuola"          => $mecc[1],
                "anno_scolastico"               => $anno_scolastico[0],
                "codice_alunno_ministeriale"    => $alunno['codice_alunno_ministeriale'],
                "progressivo_certificazioni"    => $alunno['prog_cert'],
                "descrizione_altre_certificazioni"    => $alunno['testo'],
            ];
        }

        // verifico i dati
        if ($check == 'verify') {
            if (is_array($datiStudenti)) {
                foreach ($datiStudenti as $id => $alunno) {
                    $checkStudente = $this->checkStudentsData($alunno);

                    if (!empty($checkStudente['errors'])) {
                        $errors['errors']['codice_sidi'][] = $checkStudente['errors'];
                    }
                }
            }
        }

        if (!empty($errors['errors'])) {
            return $errors;
        } else {
            return $datiStudenti;
        }
	//}}} </editor-fold>
    }

    /**
     * Verifica se lo studente ha i dati corretti
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student) {
        $errors = ['errors' => ''];

            if (strlen($student['codice_alunno_ministeriale']) == '') {
                $errors['errors'] = "Errore: codice sidi non impostato per {$student['cognome']} {$student['nome']}";
            }
//        if (strlen($student['codice_fiscale']) != 16) {
//            $errors['errors'] = "Errore: codice fiscale errato per {$student['cognome_alunno']} {$student['nome']}";
//        }
//
//        if ($student['codice_fiscale'] == '' || $student['codice_fiscale'] == 'NON CALCOLABILE NESSUN RISULTATO' ) {
//            $errors['errors'] = "Errore: manca il codice fiscale per {$student['cognome_alunno']} {$student['nome']}";
//        }

        return $errors;
    }

    /**
     * Restituisce l'ID ed il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico)
    {
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola, codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    private function formatId($id, $zeros = 4) {
        if (!isset($id) || trim($id) === '') {
            return null;
        }

        $zeroString = '';

        for ($i = 0; $i < $zeros; $i++) {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    }

    private function consiglio_orientativo($meccanografico) {

        ini_set('memory_limit', '4096M');

        $datiFinali = [];

        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => ''];

        $timestamp = date('YmdHi');
        $mecc = explode('-', $meccanografico);
        $annoScolatico = $this->getAnnoScolastico();
        $codici_scuola = $this->getCodiciScuola($meccanografico);

        // File Studenti
        // ---------------------
        $datiStudente = $this->getStudenti($meccanografico);
        file_put_contents("/tmp/studenti", print_r($datiStudente, TRUE));
        foreach ($datiStudente as $id_studente => $studente)
        {
            $dati_finali_studenti[$id_studente] = implode("|", $studente) . "|\n";
        }

        // File Area interessi
        // ---------------------
        $datiStudenteAreaInteressi = $this->getAreaInteresse($meccanografico);
        file_put_contents("/tmp/studenti_area_int", print_r($datiStudenteAreaInteressi, TRUE));
        foreach ($datiStudenteAreaInteressi as $id_studente => $studente)
        {
            $dati_finali_studenti_area_interessi[$id_studente] = implode("|", $studente) . "|\n";
        }

        // File Ambito
        // ---------------------
        $datiStudenteAmbito = $this->getAmbito($meccanografico);
        file_put_contents("/tmp/studenti_ambito", print_r($datiStudenteAmbito, TRUE));
        foreach ($datiStudenteAmbito as $id_studente => $studente)
        {
            $dati_finali_studenti_ambito[$id_studente] = implode("|", $studente) . "|\n";
        }

        // File Certificazioni
        // ---------------------
        $datiStudenteCertificazioni = $this->getCertificazioni($meccanografico);
        file_put_contents("/tmp/studenti_certificazioni", print_r($datiStudenteCertificazioni, TRUE));
        foreach ($datiStudenteCertificazioni as $id_studente => $studente)
        {
            $dati_finali_studenti_certificazioni[$id_studente] = implode("|", $studente) . "|\n";
        }


        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolatico['valore'])[0] .
                        "00CO" .
                        "MAST" .
                        "20241118" .
                        $timestamp;

        $datiFinali = [
           'studenti' => array_values($dati_finali_studenti),
           'area_interessi' => array_values($dati_finali_studenti_area_interessi),
           'ambito' => array_values($dati_finali_studenti_ambito),
           'certificazioni' => array_values($dati_finali_studenti_certificazioni),
        ];

        if (empty($result[$meccanografico]['errors_stop']))
        {
            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/consiglio_orientativo/*00COMAST20160930*");

            // Creo il file Consiglio
            $filename_studenti = $prefisso_file . '001TB_ALU_CONSORIE.TXT';
            file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/" . $filename_studenti, "");
            foreach ($datiFinali['studenti'] as $riga_studente)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/" . $filename_studenti, print_r($riga_studente, true), FILE_APPEND);
            }

            // Creo il file Area Interessi
            $filename_area = $prefisso_file . '002TB_ALU_AREACDO.TXT';
            file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/" . $filename_area, "");
            foreach ($datiFinali['area_interessi'] as $riga_studente)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/" . $filename_area, print_r($riga_studente, true), FILE_APPEND);
            }

            // Creo il file Ambito
            $filename_ambito = $prefisso_file . '003TB_ALU_ATTIVCDO.TXT';
            file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/" . $filename_ambito, "");
            foreach ($datiFinali['ambito'] as $riga_studente)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/" . $filename_ambito, print_r($riga_studente, true), FILE_APPEND);
            }

            // Creo il file Certificazioni
            $filename_certificazioni = $prefisso_file . '004TB_ALU_CERTIFCDO.TXT';
            $file_path = "/var/www-source/mastercom/tmp_siis/consiglio_orientativo/" . $filename_certificazioni;

            // Verifica se l'array è vuoto
            if (!empty($datiFinali['certificazioni'])) {
                foreach ($datiFinali['certificazioni'] as $riga_studente) {
                    file_put_contents($file_path, print_r($riga_studente, true), FILE_APPEND);
                }
            } else {
                // Crea il file con un contenuto predefinito, ad esempio una stringa vuota
                file_put_contents($file_path, "");
            }
            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/consiglio_orientativo/CONSIGLIOORIENTATIVO_' . explode('-',$meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/consiglio_orientativo/##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile) {
                if ($zip->open($zipFile) === TRUE) {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/consiglio_orientativo/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                            or die ("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/consiglio_orientativo/##*00COMAST20160930*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));
        file_put_contents('/tmp/datiFinali', print_r($datiFinali, true));

        return $result[$meccanografico]['errors_stop'];
    }
}
