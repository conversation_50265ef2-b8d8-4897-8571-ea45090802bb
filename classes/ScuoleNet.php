<?php

/*
 * Classe mutata da quella degli scrutini finali analitici per le estrazioni di ScuoleNet (ex RiminInRete)
 */

class ScuoleNet extends Siis
{

    protected $db;
    protected $pdf;
    public $flusso;
    public $database;

    public $flusso_sidi;
    public $versione;


    protected $voti_consentiti = ['NC', 'A', '', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '20', '30', '40', '50', '60', '70', '80', '90', '100','ESONERO'];

    function __construct($flusso, $database)
    {
        $this->flusso = $flusso;
        $this->database = $database;

        parent::__construct($this->flusso, $this->database);


        $this->flusso_sidi = "00VA";
        $this->versione = "20180525";
    }

    /**
     * Estrae il dato riferito al parametro ANNO_SCOLASTICO_ATTUALE
     *
     * @return string
     */
    public function getAnnoScolastico()
    {
        $query = "SELECT valore
                    FROM parametri
                    WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'";
        return $this->db->query($query);
    }

    /**
     * Restituisce l'ID ed il codice di prenotazione SIDI della scuola
     * @param type $meccanografico
     * @return type
     */
    public function getCodiciScuola($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $mecc = explode('-', $meccanografico);
        $query = "SELECT id_scuola,
                        codice_prenotazione
                    FROM scuole
                    WHERE flag_canc = 0
                        AND codice_meccanografico = '{$mecc[0]}'
                        AND codice_meccanografico_secondario = '{$mecc[1]}'";

        $result = $this->db->query($query, true);

        return $result[0];
    //}}} </editor-fold>
    }

    /**
     *
     * @param string $meccanografico
     * @return string
     */
    public function getOrdineScuola($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $mecc_secondario = explode('-', $meccanografico)[1];
        $query = "SELECT t.id_ordine_scuola AS ordine_scuola
                    FROM scuole s,
                        tipi_scuole t
                    WHERE codice_meccanografico_secondario = '{$mecc_secondario}'
                        AND s.id_tipo_scuola = t.codice
                        AND s.flag_canc = 0";

        return $this->db->query($query);
    //}}} </editor-fold>
    }

    /**
     * Estrae il dato riferito al parametro PERIODO_PAGELLA_IN_USO
     *
     * @return string
     */
    public function getPeriodoAttivo($periodo = null)
    {
        if (!$periodo)
        {
        $query = "SELECT valore
                    FROM parametri
                    WHERE nome = 'PERIODO_PAGELLA_IN_USO'";
        return $this->db->query($query);
        }
        else
        {
            return ["valore" => $periodo];
        }
    }

    function decode($field)
    {
        if (!is_array($field) && strlen($field) > 0) 
        {
            return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
        }
        else
        {
            return $field;
        }
    }

    /**
     * Estrae il dato riferito al parametro PRIMO_PERIODO_SCOLASTICO
     *
     * @return int
     */
    public function getFrazioneTemporale($periodo = null)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT valore
                    FROM parametri
                    WHERE nome = 'PRIMO_PERIODO_SCOLASTICO'";
        $arrFT = $this->db->query($query);

        $periodoAttivo = $this->getPeriodoAttivo($periodo);

        switch ($arrFT['valore'])
        {
            case 'quadrimestre':
                if (in_array((int) $periodoAttivo['valore'], [7, 27]))
                {
                    $frazioneTemp = 4;
                }
                else
                {
                    $frazioneTemp = 5;
                }
                break;
            case 'pentamestre':
                $frazioneTemp = 7;
                break;
            case 'trimestre':
                $frazioneTemp = 3;
                break;
        }

        return $frazioneTemp;

    //}}} </editor-fold>
    }

    /**
     * Verifica se lo studente ha il codice SIDI settato
     *
     * @param array $student
     * @return string
     */
    public function checkStudentsData($student)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];

        if ($student['codice_alunno_ministeriale'] == '')
        {
            $errors['errors'] = "Errore: manca il codice SIDI per {$student['cognome']} {$student['nome']} classe {$student['classe']}{$student['sezione']}. "
            . "<br> Per scaricare il codice SIDI andare in Setup C06 - ESTRAZIONE ALUNNI SOGEI, inserire le credenziali, selezionare l'istituto e premere su Procedere";
        }

        return $errors;
    //}}} </editor-fold>
    }

    /**
     * Verifica se sono presenti Quadri Orario dove non sono stati effettuati
     * gli abbinamenti in Setup A15
     *
     * @return type
     */
    public function getQuadriOrarioDaSalvare($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        
        $anno_scolastico = $this->getAnnoScolastico()['valore'];
        $mecc_secondario = explode('-', $meccanografico)[1];

//        $query = "SELECT DISTINCT descrizione
//                    FROM piani_studio
//                        LEFT JOIN abbinamento_materie_piani_studio
//                            ON piani_studio.codice_piano_studio_sidi = abbinamento_materie_piani_studio.id_quadro_orario
//                    WHERE
//                        piani_studio.anno_scolastico = '{$anno_scolastico}'
//                        AND    
//                        (
//                            codice_piano_studio_sidi NOT IN (
//                                    SELECT id_quadro_orario
//                                        FROM abbinamento_materie_piani_studio
//                                )
//                                AND codice_piano_studio_sidi IN (
//                                    SELECT DISTINCT id_quadro_orario_sidi
//                                        FROM studenti_completi
//                                        WHERE codice_meccanografico_secondario = '{$mecc_secondario}'
//                                            AND codice_ministeriale != 'FAKE'
//                                            AND esito_corrente_calcolato NOT ILIKE '%Trasferito%'
//                                            AND esito_corrente_calcolato NOT ILIKE '%Ritirato%'
//                                            AND esito_corrente_calcolato NOT ILIKE '%estero%'
//                                )
//                        )
//                        OR
//                        (
//                            abbinamento_materie_piani_studio.id_quadro_orario = piani_studio.codice_piano_studio_sidi
//                            AND (abbinamento_materie_piani_studio.id_materia, id_quadro_orario) NOT IN 
//                            (
//                                SELECT DISTINCT classi_prof_materie.id_materia,
//                                            studenti_completi.id_quadro_orario_sidi
//                                                                                    FROM studenti_completi,
//                                                                                        classi_prof_materie,
//                                                                                        materie
//                                                                                    WHERE classi_prof_materie.id_classe = studenti_completi.id_classe
//                                                                                        AND classi_prof_materie.flag_canc = 0
//                                                                                        AND studenti_completi.codice_meccanografico_secondario = '{$mecc_secondario}'
//                                                                                        --AND studenti_completi.codice_ministeriale != 'FAKE'
//                                                                                        AND studenti_completi.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
//                                                                                        AND studenti_completi.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
//                                                                                        AND studenti_completi.esito_corrente_calcolato NOT ILIKE '%estero%'
//                                                                                        AND materie.id_materia = classi_prof_materie.id_materia
//                                                                                      AND materie.flag_canc = 0
//                                                                                    )
//                            AND piani_studio.codice_piano_studio_sidi IN (SELECT DISTINCT id_quadro_orario_sidi FROM studenti_completi)
//                            AND piani_studio.anno_scolastico = '{$anno_scolastico}'
//                        )";

        $query = "SELECT DISTINCT descrizione
                    FROM piani_studio
                        LEFT JOIN abbinamento_materie_piani_studio
                            ON piani_studio.codice_piano_studio_sidi = abbinamento_materie_piani_studio.id_quadro_orario
                    WHERE
                        piani_studio.anno_scolastico = '{$anno_scolastico}'
                        AND    
                        codice_piano_studio_sidi NOT IN 
                        (
                            SELECT id_quadro_orario
                                FROM abbinamento_materie_piani_studio
                        )
                        AND codice_piano_studio_sidi IN 
                        (
                            SELECT DISTINCT id_quadro_orario_sidi
                                FROM studenti_completi
                                WHERE codice_meccanografico_secondario = '{$mecc_secondario}'
                                    AND codice_ministeriale != 'FAKE'
                                    AND esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                                    AND esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                                    AND esito_corrente_calcolato NOT ILIKE '%estero%'
                                    and classe != '5'
                        )";
                            
        return $this->db->query($query);
    //}}} </editor-fold>
    }

    /**
     * Verifica se sono presenti studenti non abbinati al Quadro Orario
     *
     * @return type
     */
    public function getStudentiSenzaQo($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $mecc_secondario = explode('-', $meccanografico)[1];
        $query = "SELECT id_studente,
                        cognome,
                        nome,
                        classe,
                        sezione,
                        descrizione_indirizzi
                    FROM studenti_completi
                    WHERE esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                        AND esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                        AND esito_corrente_calcolato NOT ILIKE '%estero%'
                        AND id_quadro_orario_sidi IS NULL
                        AND ordinamento = '0'
                        AND classe <> '5'
                        AND codice_meccanografico_secondario = '{$mecc_secondario}'
                        AND classificazione_indirizzi IN ('PR','EI','MM','PQ')
                ";
        return $this->db->query($query);
    //}}} </editor-fold>
    }

    /**
     * Restituisce i dati della scuola in questione
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function getScuola($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT scuole.id_scuola,
                                scuole.descrizione,
                                codice_meccanografico,
                                codice_meccanografico_secondario,
                                scuole.codice_prenotazione,
                                sedi.id_sede,
                                sedi.descrizione as sedi_descrizione
                            FROM scuole, sedi, indirizzi
                            WHERE scuole.id_scuola = sedi.id_scuola::int
                                AND sedi.id_sede = indirizzi.id_sede::bigint
                                AND indirizzi.id_codice_ministeriale NOT IN (
                                        SELECT id_indirizzo FROM indirizzi_ministeriali
                                        WHERE classificazione IN ('AA', 'EE')
                                    )
                                AND indirizzi.tipo_indirizzo NOT IN ('6','7')
                                AND indirizzi.flag_canc = 0
                                AND scuole.flag_canc = 0
                                AND sedi.flag_canc = 0
                                AND codice_meccanografico = '{$mecc[0]}'
                                AND codice_meccanografico_secondario = '{$mecc[1]}'
                    ";

        $datiScuola =  $this->db->query($query);

        if (array_key_exists(0, $datiScuola))
        {
            foreach ($datiScuola as $value)
            {
                if ($value['codice_prenotazione'] == '')
                {
                    $errors['errors']['codice_prenotazione'][] = "Errore: manca il codice prenotazione per la scuola {$value['codice_meccanografico']}-{$value['codice_meccanografico_secondario']}";

                    if (!empty($errors['errors']))
                    {
                        $datiScuola['errors'] = $errors['errors'];
                    }
                }
            }
        }
        else
        {
            if ($datiScuola['codice_prenotazione'] == '')
            {
                $errors['errors']['codice_prenotazione'] = "Errore: manca il codice prenotazione per la scuola {$datiScuola['codice_meccanografico']}-{$datiScuola['codice_meccanografico_secondario']}";

                if (!empty($errors['errors']))
                {
                    $datiScuola['errors'] = $errors['errors'];
                }
            }
        }

        return $datiScuola;
    //}}} </editor-fold>
    }

    /**
     * Estrae l'elenco degli studenti per la scuola in questione con i seguenti
     * filtri:
     *
     * codice_ministeriale <> 'FAKE' (Indirizzo non FAKE)
     * classi 1,2,3,4 per indirizzi Superiori tranne Classico
     * classi 1,2,4,5 per indirizzo Superiore Classico
     * classi 1,2,3 per indirizzo Medie
     * esito <> Trasferito, Ritiraro e Frequanza Anno Estero
     *
     * @return mixed
     */
    public function getStudenti($meccanografico, $periodoAttivo)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $errors = ['errors' => ''];

        $periodoAttivoMM = intval($periodoAttivo) + 20;
        
        $mecc = explode('-', $meccanografico);

        $query = "SELECT DISTINCT sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            codice_ministeriale,
                            tipo_indirizzo,
                            votazione_pagella_fine_quadrimestre,
                            votazione_pagella_fine_anno,
                            esito_prima_media,
                            esito_seconda_media,
                            esito_terza_media,
                            esito_corrente_calcolato,
                            esonero_religione,
                            pei,
                            handicap,
                            tipo_handicap,
                            grado_handicap,
                            id_quadro_orario_sidi,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            (
                                CASE WHEN tipo_indirizzo <> '1'
                                THEN sc.classe::int
                                ELSE
                                    CASE WHEN tipo_indirizzo = '1'
                                        THEN
                                            CASE
                                                WHEN sc.classe = '1' THEN 3
                                                WHEN sc.classe = '2' THEN 4
                                                WHEN sc.classe = '3' THEN 5
                                                WHEN sc.classe = '4' THEN 1
                                                WHEN sc.classe = '5' THEN 2
                                            END
                                    END
                                END
                            ) as anno_cron,
                            (
                                CASE WHEN tipo_indirizzo = '4'
                                    THEN '{$periodoAttivoMM}'
                                    ELSE '{$periodoAttivo}'
                                END
                            ) as periodo,
                            crediti_terza,
                            crediti_quarta
                        FROM studenti_completi sc, storia_studenti ss
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND tipo_indirizzo IN ('0', '1', '2', '3', '5')
                            AND sc.classificazione_indirizzi IN ('PR','EI','MM','PQ')
                            AND sc.id_studente = ss.id_studente
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'
                            AND ordinamento = '0'

                        UNION

                        SELECT DISTINCT sc.id_studente,
                            codice_alunno_ministeriale,
                            cognome,
                            nome,
                            codice_fiscale,
                            sc.classe,
                            sc.sezione,
                            codice_ministeriale,
                            tipo_indirizzo,
                            votazione_pagella_fine_quadrimestre,
                            votazione_pagella_fine_anno,
                            esito_prima_media,
                            esito_seconda_media,
                            esito_terza_media,
                            esito_corrente_calcolato,
                            esonero_religione,
                            pei,
                            handicap,
                            tipo_handicap,
                            grado_handicap,
                            id_quadro_orario_sidi,
                            codice_meccanografico,
                            codice_meccanografico_secondario,
                            (
                                CASE WHEN tipo_indirizzo <> '1'
                                THEN sc.classe::int
                                ELSE
                                    CASE WHEN tipo_indirizzo = '1'
                                        THEN
                                            CASE
                                                WHEN sc.classe = '1' THEN 3
                                                WHEN sc.classe = '2' THEN 4
                                                WHEN sc.classe = '3' THEN 5
                                                WHEN sc.classe = '4' THEN 1
                                                WHEN sc.classe = '5' THEN 2
                                            END
                                    END
                                END
                            ) as anno_cron,
                            (
                                CASE WHEN tipo_indirizzo = '4'
                                    THEN '{$periodoAttivoMM}'
                                    ELSE '{$periodoAttivo}'
                                END
                            ) as periodo,
                            crediti_terza,
                            crediti_quarta
                        FROM studenti_completi sc, storia_studenti ss
                        WHERE codice_ministeriale <> 'FAKE'
                            AND sc.codice_meccanografico = '{$mecc[0]}'
                            AND sc.codice_meccanografico_secondario = '{$mecc[1]}'
                            AND sc.classe IN ('1','2','3')
                            AND tipo_indirizzo = '4'
                            AND sc.classificazione_indirizzi IN ('PR','EI','MM','PQ')
                            AND sc.id_studente = ss.id_studente
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'
                            AND ordinamento = '0'
                        ORDER BY codice_ministeriale, classe, sezione, cognome, nome
                        ";

        $arrAlunno = $this->db->query($query);

        return $arrAlunno;

    //}}} </editor-fold>
   }

    /**
     * Estrae le competenze certificate per gli studenti delle terze dell Secondarie di I Grado
     * @param type $dati_studente
     * @param type $check
     * @return type
     */
    public function getCompetenze($dati_studente, $check = null)
    {
    //{{{ <editor-fold defaultstate="collapsed">

        $annoScolastico = $this->getAnnoScolastico();

        $datiCompetenze = [];
        // Ciclo gli studenti e tiro fuori le loro assenze
        if (!empty($dati_studente))
        {
            foreach ($dati_studente as $studente)
            {
                $id_studente = $studente['id_studente'];

                // Estraggo le competenze dello studente
                $query = "SELECT v.id_studente,
                                v.id_competenza_scolastica,
                                c.codice_competenza_sidi,
                                v.valore,
                                v.testo
                            FROM competenze_valori v,
                                competenze_scolastiche c
                            WHERE v.id_competenza_scolastica = c.id_competenza_scolastica
                                AND v.flag_canc = 0
                                AND c.flag_canc = 0
                                AND c.anno_fine = 0
                                AND c.ordine_scolastico = 'MM'
                                AND
                                (
                                    (v.valore != '' AND codice_competenza_sidi != 11)
                                    OR
                                    (v.testo != '' AND codice_competenza_sidi = 11)
                                )
                                AND v.id_studente = {$id_studente}
                            ORDER BY v.id_studente,
                                c.codice_competenza_sidi
                        ";

                $arrCompetenze = $this->db->query($query, true);

                foreach($arrCompetenze as $competenza)
                {
                    $chiave = $id_studente . $competenza['codice_competenza_sidi'];
                    $valore_anno_scolastico = explode('/', $annoScolastico['valore'])[0] . substr(explode('/', $annoScolastico['valore'])[1], 2, 2);

                    // Riga competenza
                    $datiCompetenze[$chiave]['codice_ministeriale_scuola']  = $studente['codice_meccanografico_secondario'];
                    $datiCompetenze[$chiave]['anno_scolastico']             = $valore_anno_scolastico;
                    $datiCompetenze[$chiave]['codice_alunno_sidi']          = $studente['codice_alunno_ministeriale'];
                    $datiCompetenze[$chiave]['codice_competenza']           = $competenza['codice_competenza_sidi'];
                    $datiCompetenze[$chiave]['certificazione_competenza']   = ($competenza['codice_competenza_sidi'] == 11) ? preg_replace( "/\r|\n/", "", $this->decode($competenza['testo'])) : $competenza['valore'];
                }
            }
        }
        return $datiCompetenze;
    //}}} </editor-fold>

    }

    /**
	* Formats a string to a certain length inserting a specified number of leading zeros
     *
	* @param String $id the string to format
	* @param Integer $zeros length of the formatted string to create
	* @return String the formatted string
	*/
    public function formatId($id, $zeros = 4)
    {
    //{{{ <editor-fold defaultstate="collapsed">

        if (!isset($id) || trim($id) === '')
        {
            return null;
        }

        $zeroString = '';
        for ($i = 0; $i < $zeros; $i++)
        {
            $zeroString .= '0';
        }

        return substr($zeroString . $id, -$zeros);
    //}}} </editor-fold>
    }

    /**
     * Verifica dati voti per ogni studente
     *
     * @param array $studente
     * @param array $voti
     *
     * @return mixed
     */
    public function checkVoti($studente, $voti)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $data = ['errors' => [], 'warnings' => [], 'results' => []];
        $monteoreTotale = $assenzeTotali = 0;
        $limiteAssenze = false;

        if (!is_array($voti)
                && $studente['esito_corrente_calcolato'] != 'Non ammesso alla classe successiva'
                && $studente['pei'] == 'NO' && ($studente['handicap'] == 'NO' && $studente['tipo_handicap'] == ''))
        {
            $data['errors']['voti_mancanti'] = "Nessun voto presente per lo studente {$studente['cognome']} {$studente['nome']} (classe {$studente['classe']} "
                                             . "{$studente['sezione']} - periodo {$studente['periodo']}).<br> <b>Lo studente non è stato inviato</b>.";
        }
        elseif (!is_array($voti)
                && $studente['esito_corrente_calcolato'] != 'Non ammesso alla classe successiva'
                && ($studente['pei'] == 'SI' || ($studente['handicap'] == 'SI' && $studente['tipo_handicap'] != '')))
        {
            $data['warnings']['voti_pei'] = "Mancano dei voti per lo studente {$studente['cognome']} {$studente['nome']} (classe {$studente['classe']} "
                                          . "{$studente['sezione']} - periodo {$studente['periodo']}).<br> <b>Lo studente non è stato inviato</b>.";
        }
        else
        {
            if (!is_resource($voti) && array_key_exists(0, $voti))
            {
                foreach ($voti as $voto)
                {
                    if (intval($voto['monteore_totale'] > 0) && $voto['tipo_materia'] != 'CONDOTTA')
                    {
                        $monteoreTotale += floatval($voto['monteore_totale']);
                        $assenzeTotali += floatval($voto['ore_assenza']);
                    }
                }

                if (floatval($assenzeTotali) > 0 && floatval($monteoreTotale) > 0)
                {
                    $percentualeAssemze = round($assenzeTotali / $monteoreTotale, 2) * 100;
                }

                if (intval($percentualeAssemze) >= 25)
                {
                    $limiteAssenze = true;
                }

                // Verifico i voti
                foreach ($voti as $voto)
                {
                    if (
                        ($studente['pei'] == 'SI'
                            ||
                            ($studente['handicap'] == 'SI' && $studente['tipo_handicap'] != '')
                        )
                        && $studente['esito_corrente_calcolato'] != 'Non ammesso alla classe successiva'
                        && $limiteAssenze == false
                        && ($voto['voto_pagellina'] == '' && $voto['voto_scritto_pagella'] == '' && $voto['voto_orale_pagella'] == '' && $voto['voto_pratico_pagella'] == '' )
                    )
                    {
                        $data['warnings']['voti_pei'] = "Mancano dei voti per lo studente {$studente['cognome']} {$studente['nome']} (classe {$studente['classe']} "
                                                      . "{$studente['sezione']} - periodo {$studente['periodo']}).<br> <b>Lo studente non è stato inviato</b>."
                                                      . "<br>Materia {$voto['descrizione']} ";
                        $voto['voto_pagellina'] = 'ESONERO';
                    }
                    elseif(
                            ($studente['pei'] == 'NO'
                                &&
                                ($studente['handicap'] == 'NO' && $studente['tipo_handicap'] == '')
                            )
                            && ($voto['voto_pagellina'] == '' && $voto['voto_scritto_pagella'] == '' && $voto['voto_orale_pagella'] == '' && $voto['voto_pratico_pagella'] == '' )
                            && $voto['codice_ministeriale'] != '2777'
                            && $studente['esito_corrente_calcolato'] != 'Non ammesso alla classe successiva'
                            && $limiteAssenze == false
                            &&
                            (
                                ($studente['esonero_religione'] == '1' && $voto['tipo_materia'] != 'RELIGIONE')
                                ||
                                ($studente['esonero_religione'] == '0')
                            )
                    )
                    {
                        if (!$data['errors']['alcuni_voti_mancanti'])
                        {
                            $data['errors']['alcuni_voti_mancanti'] = "Mancano dei voti per lo studente {$studente['cognome']} {$studente['nome']} (classe {$studente['classe']}"
                                                                    . " {$studente['sezione']} - periodo {$studente['periodo']}).<br> <b>Lo studente non è stato inviato</b>."
                                                                    . "<br>Materia {$voto['descrizione']} ";
                            
                        } 
                        else
                        {
                            $data['errors']['alcuni_voti_mancanti'] .= "<br>Materia {$voto['descrizione']} ";
                        }
                        continue;
                    }
                    elseif(
                            (
                                $studente['pei'] == 'NO'
                                ||
                                ($studente['handicap'] == 'NO' && $studente['tipo_handicap'] == '')
                            )
                            && $voto['voto_pagellina'] != ''
                            && $limiteAssenze == false
                            &&
                            (
                                !in_array($voto['voto_pagellina'], $this->voti_consentiti)
                                && !in_array($voto['voto_scritto_pagella'], $this->voti_consentiti) 
                                && !in_array($voto['voto_orale_pagella'], $this->voti_consentiti) 
                                && !in_array($voto['voto_pratico_pagella'], $this->voti_consentiti)
                            )
                            && !in_array($voto['codice_ministeriale'], ['6666', 'I666', '2666'])
                    )
                    {
                        $data['errors']['voti_errati'] = "Ci sono voti sbagliati per lo studente {$studente['cognome']} {$studente['nome']} (classe {$studente['classe']} "
                                                       . "{$studente['sezione']} - periodo {$studente['periodo']}).<br> <b>Lo studente non è stato inviato</b>."
                                                       . "(Materia {$voto['descrizione']}-{$voto['codice_ministeriale']})";
                        continue;
                    }

                    if (!in_array($studente['tipo_indirizzo'], ['1', '4']))
                    {
                        switch ($studente['classe'])
                        {
                            case '1':
                                if ($voto['applicabilita_primo_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '2':
                                if ($voto['applicabilita_secondo_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '3':
                                if ($voto['applicabilita_terzo_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '4':
                                if ($voto['applicabilita_quarto_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '5':
                                if ($voto['applicabilita_quinto_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    elseif ($studente['tipo_indirizzo'] == '1')
                    {
                        switch ($studente['classe'])
                        {
                            case '4':
                                if ($voto['applicabilita_primo_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '5':
                                if ($voto['applicabilita_secondo_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '1':
                                if ($voto['applicabilita_terzo_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '2':
                                if ($voto['applicabilita_quarto_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            case '3':
                                if ($voto['applicabilita_quinto_anno'] == 1)
                                {
                                    $data['results'][] = $voto;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    else
                    {
                        $data['results'][] = $voto;
                    }
                }
            }
        }

        return $data;
    //}}} </editor-fold>
    }

    /**
     * Estrae i voti di ogni studente (Medie)
     *
     * @param array $studente
     * @return mixed
     */
    public function getVotiMedie($studente)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT DISTINCT materie.id_materia,
                        descrizione,
                        codice,
                        id_quadro_orario_sidi,
                        materie.codice_ministeriale,
                        materie.descrizione as descrizione_ministeriale,
                        nome_materia_breve,
                        tipo_materia,
                        voto_pagellina,
                        voto_scritto_pagella,
                        voto_orale_pagella,
                        voto_pratico_pagella,
                        tipo_recupero,
                        esito_recupero,
                        ore_assenza,
                        monteore_totale
                    FROM voti_pagelline
                        INNER JOIN pagelline ON voti_pagelline.id_pagellina = pagelline.id_pagellina
                        INNER JOIN materie ON materie.id_materia = voti_pagelline.id_materia
                        INNER JOIN classi_prof_materie ON materie.id_materia = classi_prof_materie.id_materia
                        INNER JOIN studenti ON pagelline.id_studente = studenti.id_studente
                    WHERE pagelline.periodo = '{$studente['periodo']}'
                        AND pagelline.id_studente = {$studente['id_studente']}
                        AND classi_prof_materie.id_classe IN (
                            SELECT id_classe
                            FROM studenti_completi
                            WHERE id_studente = {$studente['id_studente']}
                        )
                        AND voti_pagelline.flag_canc = 0
                        AND pagelline.flag_canc = 0
                        AND materie.flag_canc = 0
                        AND voti_pagelline.id_materia IN (
                            SELECT id_materia
                            FROM classi_prof_materie
                            WHERE id_classe IN (
                                SELECT id_classe
                                FROM studenti_completi
                                    WHERE id_studente = {$studente['id_studente']}
                            )
                        )
                        AND (
                            in_media_pagelle = 'SI'
                            OR (
                                in_media_pagelle = 'NO'
                                AND tipo_materia = 'RELIGIONE'
                           )
                        )
                        AND tipo_materia != 'CONDOTTA'
                        AND materie.codice_ministeriale <> ''
                        AND materie.codice_ministeriale <> '-1'
                    ORDER BY materie.id_materia, descrizione";

        $arrVa = $this->db->query($query);
        $voti = $this->checkVoti($studente, $arrVa);

        return $voti;
    //}}} </editor-fold>
    }

    /**
     * Estrae i voti di ogni studente (Superiori)
     *
     * @param array $studente
     * @return mixed
     */
    public function getVotiSuperiori($studente)
    {
    //{{{ <editor-fold defaultstate="collapsed">
            $query = "SELECT DISTINCT
                            materie.id_materia,
                            descrizione,
                            codice,
                            id_quadro_orario_sidi,
                            abbinamento_materie_piani_studio.codice_ministeriale,
                            abbinamento_materie_piani_studio.dati_sidi->'DESCRIZIONE_MATERIA' as descrizione_ministeriale,
                            abbinamento_materie_piani_studio.dati_sidi->'APPLICABILITAPRIMOANNOCORSO' as applicabilita_primo_anno,
                            abbinamento_materie_piani_studio.dati_sidi->'APPLICABILITASECONDOANNOCORSO' as applicabilita_secondo_anno,
                            abbinamento_materie_piani_studio.dati_sidi->'APPLICABILITATERZOANNOCORSO' as applicabilita_terzo_anno,
                            abbinamento_materie_piani_studio.dati_sidi->'APPLICABILITAQUARTOANNOCORSO' as applicabilita_quarto_anno,
                            abbinamento_materie_piani_studio.dati_sidi->'APPLICABILITAQUINTOANNOCORSO' as applicabilita_quinto_anno,
                            nome_materia_breve,
                            tipo_materia,
                            voto_pagellina,
                            voto_scritto_pagella,
                            voto_orale_pagella,
                            voto_pratico_pagella,
                            tipo_recupero,
                            esito_recupero,
                            ore_assenza,
                            monteore_totale
                        FROM voti_pagelline
                            INNER JOIN pagelline ON voti_pagelline.id_pagellina = pagelline.id_pagellina
                            INNER JOIN materie ON materie.id_materia = voti_pagelline.id_materia
                            INNER JOIN classi_prof_materie ON materie.id_materia = classi_prof_materie.id_materia
                            INNER JOIN abbinamento_materie_piani_studio ON voti_pagelline.id_materia = abbinamento_materie_piani_studio.id_materia
                            INNER JOIN studenti ON pagelline.id_studente = studenti.id_studente
                        WHERE pagelline.periodo = '{$studente['periodo']}'
                            AND pagelline.id_studente = {$studente['id_studente']}
                            AND classi_prof_materie.id_classe IN (
                                SELECT id_classe
                                FROM studenti_completi
                                WHERE id_studente = {$studente['id_studente']}
                            )
                            AND abbinamento_materie_piani_studio.id_quadro_orario = id_quadro_orario_sidi
                            AND voti_pagelline.flag_canc = 0
                            AND pagelline.flag_canc = 0
                            AND materie.flag_canc = 0
                            AND voti_pagelline.id_materia IN (
                                SELECT id_materia
                                FROM classi_prof_materie
                                WHERE id_classe IN (
                                    SELECT id_classe
                                    FROM studenti_completi
                                        WHERE id_studente = {$studente['id_studente']}
                                )
                            )
                            AND (
                                in_media_pagelle = 'SI'
                                OR (
                                    in_media_pagelle = 'NO'
                                    AND tipo_materia = 'RELIGIONE'
                               )
                            )
                        ORDER BY materie.id_materia, descrizione";

        $arrVa = $this->db->query($query);
        $voti = $this->checkVoti($studente, $arrVa);

        return $voti;
    //}}} </editor-fold>
    }

    /**
     * Estrae le pagelline di ogni studente e controlla se sono presenti dati
     * sporchi
     *
     * @param array $studente
     * @return array
     */
    public function getPagellineDoppie($studente)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT DISTINCT id_pagellina
                    FROM pagelline
                    WHERE periodo = '{$studente['periodo']}'
                        AND id_studente = {$studente['id_studente']}
                        AND flag_canc = 0";

        $arrPa = $this->db->query($query);
        $errori = ['errors' => ''];

        if (is_array($arrPa) && count($arrPa) > 1)
        {
            $errori['errors']['pagelline_doppie'] = "Sono presenti, nella sezione Pagelle, per il periodo {$studente['periodo']} dati non salvati"
                                                  . " correttamente per lo studente {$studente['cognome']} {$studente['nome']}. Contattare l'assistenza.";
        }

        if (!empty($errori['errors']))
        {
            return $errori;
        }
    //}}} </editor-fold>
    }

    /**
     * Estrae i dati compilati in Setup A13 e controlla se è tutto compilato
     * correttamente
     *
     * @param array $studente
     * @return array
     */
    public function getAbbinamentiMedie($studente)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $queryAbbin = "SELECT DISTINCT
                            sc.cognome,
                            sc.classe,
                            sc.sezione,
                            m.id_materia,
                            m.codice_ministeriale,
                            m.descrizione as descrizione_ministeriale
                        FROM classi_prof_materie cpm, studenti_completi sc, materie m
                        WHERE  sc.id_studente = {$studente['id_studente']}
                            AND cpm.id_materia = m.id_materia
                            AND m.in_media_pagelle <> 'NV'
                            AND m.flag_canc = 0
                            AND cpm.id_classe = sc.id_classe
                            AND cpm.flag_canc = 0
                            AND cpm.itp = 'NO'
                            AND sc.ordinamento = '0'";

        $arrAbbin = $this->db->query($queryAbbin);

        $errori = ['errors' => ''];

        if (is_array($arrAbbin))
        {
            foreach ($arrAbbin as $abbinamento)
            {
                if ($abbinamento['codice_ministeriale'] == ''
                    || $abbinamento['descrizione_ministeriale'] == ''
                    || $abbinamento['codice_ministeriale'] == '-1'
                    || $abbinamento['descrizione_ministeriale'] == '-1'
                )
                {
                    $query = "SELECT descrizione FROM materie WHERE id_materia = {$abbinamento['id_materia']}";
                    $descrizione = $this->db->query($query);

                    $errori['errors']['abbinamenti_mancanti'] = "Ci sono delle classi per le quali manca il codice ministeriale "
                        . "(Classe {$abbinamento['classe']} {$abbinamento['sezione']} - Materia {$descrizione['descrizione']}). Controllare i dati in setup A3.";
                }
            }
        }

        if (!empty($errori['errors']) || !empty($errori['warning']))
        {
            return $errori;
        }
        else
        {
            return $arrAbbin;
        }
    //}}} </editor-fold>
    }

    /**
     * Estrae i dati compilati in Setup A15 e controlla se è tutto compilato
     * correttamente
     *
     * @param array $studente
     * @return array
     */
    public function getAbbinamentiSuperiori($studente)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $queryAbbin = "SELECT DISTINCT sc.cognome,
                                        sc.classe,
                                        sc.sezione,
                                        amp.codice_ministeriale,
                                        amp.dati_sidi->'DESCRIZIONE_MATERIA' as descrizione_ministeriale
                                    FROM abbinamento_materie_piani_studio amp,
                                        studenti_completi sc
                                    WHERE sc.id_studente = {$studente['id_studente']}
                                        AND amp.id_quadro_orario = sc.id_quadro_orario_sidi
                                        AND sc.ordinamento = '0'
                                ";

        $arrAbbin = $this->db->query($queryAbbin);

        $query = "SELECT DISTINCT codice_piano_studio_sidi, descrizione
                    FROM piani_studio
                    WHERE codice_piano_studio_sidi = {$studente['id_quadro_orario_sidi']}";

        $arrQuadro = $this->db->query($query);

        $errori = ['errors' => ''];

        if (!is_array($arrAbbin))
        {
            foreach ($arrQuadro as $quadro_singolo)
            {
                $msg = '<b> Controllare i dati in Setup A15 </b>';
                $errori['errors']['abbinamenti_mancanti'] = "Non sono stati salvati i dati per il quadro orario {$quadro_singolo['descrizione']}. {$msg}";
            }
        }

        if (!empty($errori['errors']))
        {
            return $errori;
        }
    //}}} </editor-fold>
    }

    /**
     * Verifica l'esistenza dei Quadri Orario sul Mastercom
     *
     * @param string $anno_scolastico
     * @return array
     */
    public function checkEsistenzaQo($anno_scolastico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $anno = $anno_scolastico['valore'];

        $query = "SELECT * FROM piani_studio WHERE codice_piano_studio_sidi IS NOT NULL AND anno_scolastico = '{$anno}'";
        $arrQuadro = $this->db->query($query);

        $errori = ['errors' => ''];

        if (!is_array($arrQuadro))
        {
            $errori['errors']['mancano_quadri_orario'][] = "Utilizzare la funzione ESTRAZIONE QUADRI ORARIO (Secondarie II Grado) in Setup C06 - Flussi comunicazione progetto SIIS per scaricare i Quadri Orario sul Mastercom. "
                    . "<br>Successivamente abbinare i Quadri Orario alle materie Mastercom (Setup A15 - Gestione abbinamenti Materie-Quadri Orario per Sec. II Grado) ed infine abbinarli agli studenti (Setup A14 - Gestione abbinamenti Studente-Quadri Orario per Sec. II Grado)";
        }

        if (!empty($errori['errors']))
        {
            return $errori;
        }
    //}}} </editor-fold>
    }


    /**
     * Estrae i dati compilati in Setup A15 e controlla se è tutto compilato
     * correttamente (viene utilizzato come controllo preventivo prima di ciclare
     * gli studenti)
     *
     * @return array
     */
    public function checkAbbinamentiQO()
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT DISTINCT codice_piano_studio_sidi as codice, descrizione
                    FROM piani_studio
                    WHERE codice_piano_studio_sidi IN (
                            SELECT DISTINCT id_quadro_orario_sidi FROM studenti_completi
                        )
                        AND codice_piano_studio_sidi IS NOT NULL";

        $arrQuadro = $this->db->query($query);

        $errori = ['errors' => ''];

        if (!empty($arrQuadro))
        {
            foreach ($arrQuadro as $quadro)
            {
                $query = "SELECT id_quadro_orario FROM abbinamento_materie_piani_studio WHERE id_quadro_orario = {$quadro['codice']}";
                $result = $this->db->query($query);

                // Cambia poi in if (empty($result)). Aggiunto il ! per debug
                if (empty($result))
                {
                    $msg = '<b> Controllare i dati in Setup A15 </b>';
                    $errori['errors']['abbinamenti_mancanti'][] = "Non sono stati salvati i dati per il quadro orario {$quadro['descrizione']}. {$msg}";
                }
            }
        }

        if (!empty($errori['errors']))
        {
            return $errori;
        }
    //}}} </editor-fold>
    }

    /**
     * Controlla se esistono materie abbinate alla classe e non abbinate
     * ai Quadri Orario che vengono utilizzate per il calcolo della media e
     * la visualizzazione in pagella
     *
     * @return array
     */
    public function checkMaterieNonAbbinateQO()
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT DISTINCT c.id_classe,
                                c.classe,
                                c.sezione,
                                i.descrizione as indirizzo,
                                m.codice_ministeriale as codice,
                                m.descrizione as materia
                            FROM materie m, classi c,
                                classi_prof_materie cpm,
                                indirizzi i,
                                indirizzi_ministeriali im
                            WHERE m.id_materia = cpm.id_materia
                                AND cpm.id_classe = c.id_classe
                                AND c.id_indirizzo = i.id_indirizzo
                                AND i.id_codice_ministeriale = im.id_indirizzo
                                AND cpm.id_materia = m.id_materia
                                AND m.id_materia NOT IN (
                                    SELECT id_materia
                                        FROM abbinamento_materie_piani_studio
                                )
                                AND im.codice <> 'FAKE'
                                AND
                                (
                                    (
                                        c.classe IN ('1','2','3','4')
                                        AND i.tipo_indirizzo IN ('0','2','3','5')
                                    )
                                    OR
                                    (
                                        c.classe IN ('1','2','4','5')
                                        AND i.tipo_indirizzo = '1'
                                    )
                                    OR
                                    (
                                        c.classe IN ('1','2','3')
                                        AND i.tipo_indirizzo = '4'
                                    )
                                )
                                AND im.classificazione IN ('PR','EI','MM','PQ')
                                AND m.flag_canc = 0
                                AND c.flag_canc = 0
                                AND i.flag_canc = 0
                                AND m.codice_ministeriale <> ''
                                AND m.in_media_pagelle <> 'NV'
                            ORDER BY i.descrizione, c.classe, c.sezione";

        $arrMaterie = $this->db->query($query);

        $array = [];

        foreach ($arrMaterie as $materie)
        {
            $array[$materie['id_classe']]['classe'] = $materie['classe'].'°'.$materie['sezione'].' '.$materie['indirizzo'];
            $array[$materie['id_classe']]['materie'][] = "Materia {$materie['materia']} Codice {$materie['codice']}";
        }

        return $array;
    //}}} </editor-fold>
    }

    /**
     * Controlla se esistono materie abbinate ai Quadri Orario che
     * non vengono utilizzate per il calcolo della media e la visualizzazione
     * in pagella (NV)
     *
     * @return array
     */
    public function checkMaterieNV()
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT DISTINCT m.descrizione,
                        m.in_media_pagelle
                     FROM abbinamento_materie_piani_studio am,
                        materie m
                     WHERE am.id_materia = m.id_materia
                        AND m.flag_canc = 0
                        AND m.in_media_pagelle <> 'SI'
                        AND m.tipo_materia <> 'RELIGIONE'
                        AND m.tipo_materia <> 'ALTERNATIVA'
                ";
        $materie = $this->db->query($query);

        $errori = ['errors' => []];

        if (is_array($materie))
        {
            if (array_key_exists(0, $materie))
            {
                foreach ($materie as $materia)
                {
                    switch ($materia['in_media_pagelle'])
                    {
                        case 'NO':
                            $utilizzo = 'Non utilizzata nel calcolo della media';
                            break;
                        case 'NV':
                            $utilizzo = 'Non utilizzata nel calcolo della media e non stampata in pagella';
                            break;
                        default:
                            break;
                    }

                    $errori['errors']['utilizzo_materie_in_pagella'][] = "La materia {$materia['descrizione']} è stata impostata sul Mastercom come '{$utilizzo}', quindi i voti relativi a questa materia non verranno inviati a Sidi.";
                }
            }
            else
            {
                switch ($materie['in_media_pagelle'])
                {
                    case 'NO':
                        $utilizzo = 'Non utilizzata nel calcolo della media';
                        break;
                    case 'NV':
                        $utilizzo = 'Non utilizzata nel calcolo della media e non stampata in pagella';
                        break;
                    default:
                        break;
                }

                $errori['errors']['utilizzo_materie_in_pagella'][] = "La materia {$materie['descrizione']} è stata impostata sul Mastercom come '{$utilizzo}', quindi i voti relativi a questa materia non verranno inviati a Sidi.";
            }
        }

        if (!empty($errori['errors']))
        {
            return $errori;
        }
    //}}} </editor-fold>
    }

    /**
     * Verifica se all'interno della classe ci sono studenti con QO completamente
     * differenti (es. Amm. Finanza e Marketing e Sistemi Informativi Aziendali)
     *
     * @param array $meccanografico
     * @return array
     */
    public function checkQoDifferenti($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $mecc_secondario = explode('-', $meccanografico)[1];
        $elencoQoClassi = $elencoQoMultipli = [];

        $queryClassi = "SELECT DISTINCT id_classe,
                                    classe,
                                    sezione,
                                    i.descrizione as indirizzo
                                FROM classi c,
                                    indirizzi i,
                                    indirizzi_ministeriali im
                                WHERE c.id_indirizzo = i.id_indirizzo
                                    AND c.flag_canc = 0
                                    AND i.flag_canc = 0
                                    AND c.id_classe IN (
                                        SELECT id_classe
                                            FROM classi_studenti
                                    )
                                    AND c.id_classe IN (
                                        SELECT id_classe
                                            FROM classi_prof_materie
                                    )
                                    AND i.id_codice_ministeriale = im.id_indirizzo
                                    AND im.codice <> 'FAKE'
                                    AND im.classificazione IN ('PR','EI','MM','PQ')
                                    AND
                                    (
                                        (
                                            c.classe IN ('1','2','3','4')
                                            AND i.tipo_indirizzo IN ('0','2','3','5')
                                        )
                                        OR
                                        (
                                            c.classe IN ('1','2','4','5')
                                            AND i.tipo_indirizzo = '1'
                                        )
                                        OR
                                        (
                                            c.classe IN ('1','2','3')
                                            AND i.tipo_indirizzo = '4'
                                        )
                                    )
                                AND c.id_classe IN (
                                    SELECT DISTINCT id_classe
                                        FROM studenti_completi
                                        WHERE codice_meccanografico_secondario = '{$mecc_secondario}'
                                )
                                ORDER BY i.descrizione,
                                    classe,
                                    sezione
                        ";

        $elencoClassi = $this->db->query($queryClassi, true);

        foreach ($elencoClassi as $classe)
        {
            $query = "SELECT DISTINCT codice_piano_studio_sidi as codice,
                            descrizione,
                            classe,
                            sezione,
                            descrizione_indirizzi
                        FROM piani_studio ps,
                            studenti_completi sc
                        WHERE sc.id_quadro_orario_sidi = ps.codice_piano_studio_sidi
                            AND sc.id_studente NOT IN (
                                SELECT id_studente
                                    FROM classi_studenti
                                    WHERE id_classe IN (
                                        SELECT id_classe
                                            FROM classi
                                            WHERE ordinamento <> '0'
                                                AND flag_canc = 0
                                    )
                                    AND flag_canc = 0
                            )
                            AND descrizione NOT ILIKE '%LINGU%'
                            AND sc.id_classe = {$classe['id_classe']}
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Trasferito%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%Ritirato%'
                            AND sc.esito_corrente_calcolato NOT ILIKE '%estero%'
                        ORDER BY descrizione_indirizzi,
                            classe,
                            sezione
            ";

            $elencoQo = $this->db->query($query, true);

            if (is_array($elencoQo) && !empty($elencoQo))
            {
                foreach ($elencoQo as $value)
                {
                    $elencoQoClassi[$classe['id_classe']][] = $value['codice'].'-'.$value['descrizione'];
                }
            }
        }

        foreach ($elencoQoClassi as $idClasse => $qo)
        {
            if (count($elencoQoClassi[$idClasse]) > 1)
            {
                $query = "SELECT DISTINCT classe,
                                    sezione,
                                    i.descrizione
                                FROM classi c,
                                    indirizzi i
                                WHERE c.id_indirizzo = i.id_indirizzo
                                    AND c.flag_canc = 0
                                    AND i.flag_canc = 0
                                    AND c.id_classe = {$idClasse}
                                ORDER BY i.descrizione,
                                    classe,
                                    sezione
                        ";

                $qoMultipli = $this->db->query($query);
                foreach ($qo as $value)
                {
                    $elencoQoMultipli[] = "La classe {$qoMultipli['classe']}° {$qoMultipli['sezione']} {$qoMultipli['descrizione']} ha diversi QO: {$value}";
                }
            }
        }

        return $elencoQoMultipli;
    //}}} </editor-fold>
    }

    /**
     * Estrae il dato riferito al parametro TIPO_VISUALIZZAZIONE_MINUTI_ASSENZA
     *
     * @return int
     */
    public function getPeriodi()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'TIPO_VISUALIZZAZIONE_MINUTI_ASSENZA'";
        return $this->db->query($query);
    }

    /**
     * Estrae il dato riferito al parametro SCALA_VOTI
     *
     * @return int
     */
    public function getScalaVoti()
    {
        $query = "SELECT valore FROM parametri WHERE nome = 'SCALA_VOTI'";
        $arrScalaVoti = $this->db->query($query);
        return $arrScalaVoti['valore'];
    }

    /**
     * Crea lo zip per ogni file .txt generato
     *
     */
    public function createZip($fileName, $codMecc, $timestamp = null, $zipname = 'SCRUTINI_FINALI')
    {
    //{{{ <editor-fold defaultstate="collapsed">

        $timestamp2 = date('Ymd');
        $timestamp = !$timestamp ? date('YmdHi') : $timestamp;

        exec("rm /var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/{$zipname}_{$codMecc}_{$timestamp2}*.zip");

        // recupero tutti i file con prefisso giusto
        $glob = glob("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/{$fileName}*.TXT");

        foreach ($glob as $file)
        {
            $basename = basename($file);
            $name = str_replace('.TXT', '', $basename);
            $zip = new ZipArchive;
            $nomeZip = "/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/{$zipname}_{$codMecc}_{$timestamp}.zip";

            if ($zip->open($nomeZip, ZIPARCHIVE::CREATE) !== TRUE)
            {
                exit("Impossibile creare il file zip");
            }

            $zip->addFile($file, $name . ".TXT");
            $zip->close();
        }

        exec("rm /var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/*.TXT");
    //}}} </editor-fold>
    }

    /**
     * Unisce i dati contenuti nei file .txt di diversi zip che hanno lo stesso
     * nome. (serve per le scuole che hanno n server e lo stesso codice meccanografico)
     *
     * @param string $codMecc
     */
    public function mergeFile($codiceMeccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $timestamp = date('YmdHi');
        $timestamp2 = date('Ymd');
        $codMecc = explode('-', $codiceMeccanografico)[1];

        $path_files = "/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/";

        // creo i file finali
        $annoScolastico = $this->getAnnoScolastico();
        $codici_scuola = $this->getCodiciScuola($codiceMeccanografico);
        
        $prefisso_file = explode('-', $codiceMeccanografico)[1] .
                $codici_scuola['codice_prenotazione'] .
                $this->formatId($codici_scuola['id_scuola']) .
                explode("/", $annoScolastico['valore'])[0] .
                $this->flusso_sidi .
                "MAST" .
                $this->versione .
                $timestamp;  

        $glob = glob($path_files . "SCRUTINI_FINALI_{$codMecc}_{$timestamp2}*.zip");

        // Pulisco la cartella di file vecchi
        exec("rm {$path_files}{$prefisso_file}*.TXT");
        
        // recupero i due file: quello creato in locale e quello creato sull'altro server
        foreach ($glob as $key => $file) 
        {

            $basename = basename($file);
            $zip = new ZipArchive;
            $nomeZip = $path_files . $basename;

            if ($zip->open($nomeZip) == TRUE) {

                exec("mkdir {$path_files}tmp/{$key}/");

                $zip->extractTo("{$path_files}tmp/{$key}/");
                $zip->close();

                $glob_zip = glob("{$path_files}tmp/{$key}/*");

                foreach ($glob_zip as $file_singolo)
                {
                    if (strpos($file_singolo, "001TB_ALU_MAT_VALUT.TXT") !== false)
                    {
                        // Valutazioni
                        $file_scuole = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}001TB_ALU_MAT_VALUT.TXT", print_r($file_scuole, true), FILE_APPEND);
                    }

                    if (strpos($file_singolo, "002TB_ALU_ASSENZE.TXT") !== false)
                    {
                        // Assenze
                        $file_studenti = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}002TB_ALU_ASSENZE.TXT", print_r($file_studenti, true), FILE_APPEND);
                    }

                    if (strpos($file_singolo, "003TB_ALU_CERT_COMP.TXT") !== false)
                    {
                        // Competenze
                        $file_curriculum = file_get_contents($file_singolo);
                        file_put_contents("{$path_files}{$prefisso_file}003TB_ALU_CERT_COMP.TXT", print_r($file_curriculum, true), FILE_APPEND);
                    }
                }
                exec("rm {$path_files}tmp/{$key}/*");
                exec("rmdir {$path_files}tmp/{$key}");
            }
            else
            {
                echo 'Merge files failed';
            }
        }

        exec("rmdir {$path_files}tmp");
        
        // creo lo zip finale
        $this->createZip($prefisso_file, $codMecc, $timestamp);

        // creo il .dat
        file_put_contents("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/##" . $prefisso_file . ".dat", "");

        $fileZip = glob($path_files . 'SCRUTINI_FINALI_' . $codMecc . '_' . $timestamp . '.zip');
        $zip = new ZipArchive;

        foreach ($fileZip as $zipFile)
        {
            if ($zip->open($zipFile) === TRUE) 
            {
                $zip->addFile('/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                        or die ("ERROR: Could not add file: {$prefisso_file}");
                $zip->close();
            }
        }

        exec("rm {$path_files}{$prefisso_file}*.dat");
    //}}} </editor-fold>
    }

    /*
     * Estrae i dati delle valutazioni degli studenti
     * 
     */
    public function getNewValutazioni($datiStudente, $meccanografico, $periodo = 9)
    {
    //{{{ <editor-fold defaultstate="collapsed">

        $result[$meccanografico] = ['correct' => '', 'errors_stop' => '', 'warning' => '', 'valutazioni' => ''];
        $datiFlusso = [];
        
        $annoScolastico = $this->getAnnoScolastico();
        $frazioneTemporale = $this->getFrazioneTemporale($periodo);
        $periodi = $this->getPeriodi();
        $scalaVoti = $this->getScalaVoti();        
        

        $i = 0;

        foreach ($datiStudente as $studente)
        {
            $materie = [];
            $pagellineDoppie = $this->getPagellineDoppie($studente);

            if (!empty($pagellineDoppie['errors']))
            {
                $result[$meccanografico]['errors_stop'][] = [
                    'desc' => 'Dati errati nella sezione pagelle',
                    'valore' => $pagellineDoppie['errors']['pagelline_doppie']
                ];
            }

            // Da verificare con DB scuola media
            if ($studente['tipo_indirizzo'] == '4')
            {
                $abbinamenti_errati_medie = $this->getAbbinamentiMedie($studente);

                if (!empty($abbinamenti_errati_medie['errors']))
                {
                    foreach ($abbinamenti_errati_medie['errors'] as $value)
                    {
                        $result[$meccanografico]['errors_stop'][] = [
                            'desc' => 'Abbinamenti errati medie',
                            'valore' => $value
                        ];
                    }
                }

                if (!empty($abbinamenti_errati_medie['errors']))
                {
                    continue;
                }
            }

            if ($studente['tipo_indirizzo'] == '4')
            {
                $voti = $this->getVotiMedie($studente);
            }
            else
            {
                $voti = $this->getVotiSuperiori($studente);
            }

            if ($studente['tipo_indirizzo'] == '4')
            {
                // 2018/07/04 
                // Forzo gli studenti delle medie in un indirizzo ordinario, se poi hanno 
                // una materia musicale (codice 2014) allora li metto in un indirizzo musicale
                $studente['codice_ministeriale'] = 'MX01';
                
                foreach ($voti['results'] as $voto)
                {
                    if ($voto['codice_ministeriale'] == '2014' && $voto['voto_pagellina'] != '')
                    {
                        $studente['codice_ministeriale'] = 'MX02';
                        continue;
                    }
                }
            }

            if (!empty($voti['errors']))
            {
                if (!empty($voti['errors']['voti_mancanti']))
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc' => 'Voti mancanti',
                        'valore' => $voti['errors']['voti_mancanti']
                    ];
                }

                if (!empty($voti['errors']['alcuni_voti_mancanti']))
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc' => 'Alcuni voti mancanti',
                        'valore' => $voti['errors']['alcuni_voti_mancanti']
                    ];
                }

                if (!empty($voti['errors']['voti_errati']))
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc' => 'Voti Errati',
                        'valore' => $voti['errors']['voti_errati']
                    ];
                }

                continue;
            }

            if (!empty($voti['results']))
            {
                foreach ($voti['results'] as $singolaMateria)
                {
                    
                    // Recupero il periodo per individuare il campo da usare
                    // e la tipologia di voto in caso sia personalizzato
                    $campo_tipo_valutazione = $periodo == 7 ? $studente["votazione_pagella_fine_quadrimestre"] : $studente["votazione_pagella_fine_anno"];
                    
                    // Se il tipo valutazione è personalizzato sovrascrivo il tipo
                    if ($campo_tipo_valutazione == 4)
                    {
                        $query = "SELECT tipo_voto_personalizzato
                                    FROM materie
                                    WHERE id_materia = {$singolaMateria['id_materia']}";
                        $res = pgsql_query($query);
                        $tipo_voto = pg_fetch_row($res, 0)[0];
                        
                        if($tipo_voto > 0)
                        {
                            $campo_tipo_valutazione = $tipo_voto;
                        }
                    }

//                    print "<pre>";
//                    print_r($singolaMateria);
//                    print "</pre>";
                    
                    switch ($campo_tipo_valutazione)
                    {
                        case 2:
                            // Scritto e Orale
                            $singolaMateria['tipo_voto_usato'] = "SCRITTO";
                            $singolaMateria['voto'] = $singolaMateria['voto_scritto_pagella'];
                            $materie[$singolaMateria['codice_ministeriale'].$singolaMateria['id_materia'] . "SCRITTO"] = $singolaMateria;

                            $singolaMateria['tipo_voto_usato'] = "ORALE";
                            $singolaMateria['voto'] = $singolaMateria['voto_orale_pagella'];
                            $materie[$singolaMateria['codice_ministeriale'].$singolaMateria['id_materia'] . "ORALE"] = $singolaMateria;
                            
                            break;
                        case 3:
                            //Scritto, Orale, Pratico
                            $singolaMateria['tipo_voto_usato'] = "SCRITTO";
                            $singolaMateria['voto'] = $singolaMateria['voto_scritto_pagella'];
                            $materie[$singolaMateria['codice_ministeriale'].$singolaMateria['id_materia'] . "SCRITTO"] = $singolaMateria;

                            $singolaMateria['tipo_voto_usato'] = "ORALE";
                            $singolaMateria['voto'] = $singolaMateria['voto_orale_pagella'];
                            $materie[$singolaMateria['codice_ministeriale'].$singolaMateria['id_materia'] . "ORALE"] = $singolaMateria;

                            $singolaMateria['tipo_voto_usato'] = "PRATICO";
                            $singolaMateria['voto'] = $singolaMateria['voto_pratico_pagella'];
                            $materie[$singolaMateria['codice_ministeriale'].$singolaMateria['id_materia'] . "PRATICO"] = $singolaMateria;
                            break;
//                        case 4:
                            // Personalizzato (da gestire)
                             
//                            break;
                        case 1:
                        default:
                            // Voto unico di default
                            
                            // Cambia il nome del tipo valutazione a seconda del periodo
                            $descrizione_tipo_voto = $periodo == 9 ? "FINALE" : "COMPLESSIVO";
                            $singolaMateria['tipo_voto_usato'] = $descrizione_tipo_voto;
                            $singolaMateria['voto'] = $singolaMateria['voto_pagellina'];
                            $materie[$singolaMateria['codice_ministeriale'].$singolaMateria['id_materia'] . $descrizione_tipo_voto] = $singolaMateria;
                            break;
                        
                    }
                }
            }

            $assenzePagella = $monteorePagella = $votoNumerico = 0;
            $almenoUnVoto = $votiInsuff = $recupero = false;
            $tipiRecupero = true;
            $esitoRecupero = 'SI';

            foreach ($materie as $voto)
            {
                if ($voto['monteore_totale'] > 0 && $voto['tipo_materia'] != 'CONDOTTA')
                {
                    $assenzePagella += $voto['ore_assenza'];
                    $monteorePagella += $voto['monteore_totale'];
                }

                if ($voto['voto'] != '')
                {
                    $almenoUnVoto = true;

                    if ($scalaVoti == 'CENTINAIA' && is_numeric($voto['voto']))
                    {
                        $voto['voto'] = $voto['voto'] / 10;
                    }

                    if (
                        !empty($voto['voto'])
                        && (floatval($voto['voto']) < 6 || !is_numeric($voto['voto']))
                        && $voto['voto'] !== 'ESONERO'
                    )
                    {
                        if ($voto['tipo_recupero'] == '' || $voto['esito_recupero'] != 'SI')
                        {
                            if ($voto['tipo_recupero'] == '')
                            {
                                $tipiRecupero = false;
                            }
                            else
                            {
                                $recupero = true;
                            }

                            if ($voto['esito_recupero'] == '')
                            {
                                $esitoRecupero = '';
                            }
                            elseif ($voto['esito_recupero'] != 'SI' && $esitoRecupero == 'SI')
                            {
                                $esitoRecupero = 'NO';
                            }

                            $votiInsuff = true;
                        }
                    }
                    else
                    {
                        if ($voto['esito_recupero'] != '')
                        {
                            $recupero = true;
                        }
                    }
                }

                if (is_numeric($voto['voto']))
                {
                    $votoNumerico = 1;
                }
            }

            if ($monteorePagella > 0)
            {
                $percentualeTotmin = round($assenzePagella / $monteorePagella * 100);
            }
            else
            {
                $percentualeTotmin = 0;
            }

            if ($studente['tipo_indirizzo'] == 4)
            {
                $esitoIntegrazione = '';
                
                switch ((int) $studente['classe'])
                {
                    case 1:
                        //AMMESSO/A ALLA CLASSE SUCCESSIVA
                        //NON AMMESSO/A ALLA CLASSE SUCCESSIVA
                        $esitoFineAnno = $studente['esito_prima_media'] == 'SI' ? 2 : 11;
                        break;

                    case 2:
                        //AMMESSO/A ALLA CLASSE SUCCESSIVA
                        //NON AMMESSO/A ALLA CLASSE SUCCESSIVA
                        $esitoFineAnno = $studente['esito_seconda_media'] == 'SI' ? 2 : 11;
                        break;

                    case 3:
                        if ($studente['esito_terza_media'] == 'SI')
                        {
                            if ($studente['giudizio_sintetico_esame_terza_media'] != '')
                            {
                                //LICENZIATO/A
                                //NON LICENZIATO/A
                                $esitoFineAnno = $studente['giudizio_sintetico_esame_terza_media'] >= 6 ? 7 : 14;
                            }
                            else
                            {
                                //AMMESSO/A ALL'ESAME DI STATO
                                $esitoFineAnno = 1;
                            }
                        }
                        else
                        {
                            //NON AMMESSO/A ALL'ESAME DI STATO
                            $esitoFineAnno = 10;
                        }
                        break;
                }
            }
            else
            {
                if ($almenoUnVoto)
                {
                    //tutte le materie in media pagelle hanno un voto
                    if (!($votiInsuff))
                    {
                        //Non ho nessun voto insufficiente
                        if ($recupero && $esitoRecupero == 'SI')
                        {
                            //Ho dei recuperi con esito positivo
                            $esitoFineAnno = 46;
                            $esitoIntegrazione = 22;
                        }
                        else
                        {
                            //Lo studente non ha fatto recuperi
                            $esitoFineAnno = 22;
                            $esitoIntegrazione = '';
                        }
                    }
                    elseif (!($tipiRecupero))
                    {
                        //Ho delle insufficienze ma almeno una di esse non ha un tipo di recupero
                        $esitoFineAnno = 27;
                        $esitoIntegrazione = '';
                    }
                    elseif (in_array($esitoRecupero, ['NO', 'ASSENTE', 'NI']))
                    {
                        //Ho delle insufficenze, tutte con recupero compilato, e almeno un recupero negativo
                        $esitoFineAnno = 46;
                        $esitoIntegrazione = 27;
                    }
                    elseif ($esitoRecupero == '')
                    {
                        //Ho delle insufficenze, tutte con recupero, ma di cui almeno uno non compilato
                        $esitoFineAnno = 46;
                        $esitoIntegrazione = '';
                    }
                    elseif ($esitoRecupero == 'SI')
                    {
                        //Ho delle insufficenze, tutte con recupero compilato, e tutti i recuperi positivi... non dovrei mai cadere in questo caso
                        $esitoFineAnno = 46;
                        $esitoIntegrazione = 22;
                    }
                }
                else
                {
                    //NON AMMESSO/A
                    $esitoFineAnno = 27;
                    $esitoIntegrazione = '';
                }

                if ($studente['pei'] == 'SI' || ($studente['handicap'] == 'SI' && $studente['tipo_handicap'] != ''))
                {
                    if (strtolower($studente['esito_corrente_calcolato']) == 'non ammesso alla classe successiva')
                    {
                        //NON AMMESSO/A
                        $esitoFineAnno = 27;
                        $esitoIntegrazione = '';
                    }
                    elseif (strtolower($studente['esito_corrente_calcolato']) == 'ammesso alla classe successiva')
                    {
                        //AMMESSO/A
                        $esitoFineAnno = 22;
                        $esitoIntegrazione = '';
                    }
                    elseif (strtolower($studente['esito_corrente_calcolato']) == 'giudizio sospeso')
                    {
                        //GIUDIZIO SOSPESO
                        $esitoFineAnno = 46;

                        if ($recupero && $esitoRecupero == 'SI')
                        {
                            $esitoIntegrazione = 22;
                        }
                        else
                        {
                            $esitoIntegrazione = 27;
                        }
                    }
                }

                // Controllo se è giudizio sospeso anche in caso di esame di qualifica
                if (strpos($studente['esito_corrente_calcolato'],'sospeso') > 0)
                {
                    $esitoFineAnno = 46;
                }
                elseif (strpos($studente['esito_corrente_calcolato'],'Non ammesso alla classe successiva') !== false)
                {
                    $esitoFineAnno = 27;
                }
                else
                {
                    $esitoFineAnno = 22;
                }
            }

            if ($this->extra['calcolo_assenze'] != 'NO')
            {
                if ($percentualeTotmin >= 25)
                {
                    //L’alunno non ha frequentato per almeno tre quarti dell’orario annuale, ma ha usufruito della deroga
                    //L’alunno non ha frequentato per almeno tre quarti dell’orario annuale
                    $validitaAs = $almenoUnVoto ? 1 : 2;
                }
                else
                {
                    //L’alunno ha frequentato per almeno tre quarti dell’orario annuale
                    $validitaAs = 0;
                }
            }
            else
            {
                //L’alunno ha frequentato per almeno tre quarti dell’orario annuale (default)
                $validitaAs = 0;
            }

            //considerare anche tutti NC e 0 ore di assenza
            if (
                    ($validitaAs == 2 || $esitoFineAnno == 27 || $almenoUnVoto) && (
                    ($percentualeTotmin > 0 && $percentualeTotmin <= 25) || ($votoNumerico == 1 && ($percentualeTotmin >= 25 || $percentualeTotmin == 0))
                    )
            )
            {
                foreach ($materie as $voto)
                {
                    $descrizioneMateria = $voto['descrizione'];

                    if ($scalaVoti == 'CENTINAIA' && is_numeric($voto['voto']))
                    {
                        $voto['voto'] = $voto['voto'] / 10;
                    }

                    if (is_numeric($voto['voto']))
                    {
                        $voto['voto'] = intval($voto['voto']);
                    }

                    if (in_array($voto['codice_ministeriale'], ['2666', 'I666', '6666']))
                    {
                        switch ($voto['voto'])
                        {
                            case '1':
                            case '2':
                            case '3':
                            case '4':
                            case '5':
                                $descrizioneVoto = 'Insufficiente';
                                break;
                            case '6':
                                $descrizioneVoto = 'Sufficiente';
                                break;
                            case '7':
                                $descrizioneVoto = 'Buono';
                                break;
                            case '8':
                                $descrizioneVoto = 'Molto';
                                break;
                            case '9':
                                $descrizioneVoto = 'Moltissimo';
                                break;
                            case '9.25':
                                $descrizioneVoto = 'Distinto';
                                break;
                            case '10':
                                $descrizioneVoto = 'Ottimo';
                                break;
                            default:
                                // PRIMA C'ERA OTTIMO MA ENTRA IN
                                // IN QUESTO CASO SOLO SE E' VUOTO
                                $descrizioneVoto = '';
                                break;
                        }
                    }
                    else
                    {
                        if (in_array($voto['codice_ministeriale'], ['2014', '2015']))
                        {
                            $descrizioneMateria = $voto['nome_materia_breve'];
                        }

                        switch ($voto['voto'])
                        {
                            case 'NC':
                            case 'N.C.':
                                $descrizioneVoto = 'NON CLASSIFICATO';
                                break;
                            case '':
                                $descrizioneVoto = 'ESONERO';
                                break;
                            case '1':
                                $descrizioneVoto = 'UNO';
                                break;
                            case '2':
                                $descrizioneVoto = 'DUE';
                                break;
                            case '3':
                                $descrizioneVoto = 'TRE';
                                break;
                            case '4':
                                $descrizioneVoto = 'QUATTRO';
                                break;
                            case '5':
                                $descrizioneVoto = 'CINQUE';
                                break;
                            case '6':
                                $descrizioneVoto = 'SEX';
                                break;
                            case '7':
                                $descrizioneVoto = 'SETTE';
                                break;
                            case '8':
                                $descrizioneVoto = 'OTTO';
                                break;
                            case '9':
                                $descrizioneVoto = 'NOVE';
                                break;
                            case '10':
                                $descrizioneVoto = 'DIECI';
                                break;
                            default:
                                $descrizioneVoto = 'ESONERO';
                                break;
                        }
                    }

                    switch ($periodi['valore'])
                    {
                        case 'PERIODI_50_R':
                            $numeroAssenze = round(intval($voto['ore_assenza']) / 50);
                            break;
                        case 'PERIODI_55_R':
                            $numeroAssenze = round(intval($voto['ore_assenza']) / 55);
                            break;
                        case 'PERIODI_50_T':
                            $numeroAssenze = floor(intval($voto['ore_assenza']) / 50);
                            break;
                        case 'PERIODI_55_T':
                            $numeroAssenze = floor(intval($voto['ore_assenza']) / 55);
                            break;
                        case 'ORE_ARROTONDATE':
                            $numeroAssenze = round(intval($voto['ore_assenza']) / 60);
                            break;
                        case 'ETICHETTA_ORE_MINUTI':
                        case 'ORE_MINUTI':
                        case 'ORE_DECIMALI':
                        case 'ORE_ARROTONDATE':
                        default:
                            $numeroAssenze = intval(intval($voto['ore_assenza']) / 60);
                            break;
                    }

                    if ($esitoFineAnno == 47)
                    {
                        $numeroAssenze = '';
                    }

                    if ($studente['tipo_indirizzo'] != 4
                        && ($esitoFineAnno == 22 || $esitoIntegrazione != '')
                        &&
                        (
                            in_array($studente['classe'], [3, 4])
                            ||
                            ($studente['tipo_indirizzo'] == 1 && in_array($studente['classe'], [1, 2]))
                        )
                    )
                    {

                        if (in_array($studente['classe'], [1, 3]))
                        {
                            // Terzo anno scolastico
                        $creditoScolastico = $studente['crediti_terza'];
                    }
                    else
                    {
                            // Quarto anno scolastico
                            $creditoScolastico = $studente['crediti_quarta'];
                        }
                    }
                    else
                    {
                        $creditoScolastico = '';
                    }

                    $descrizioneMinisteriale = str_replace('Â°', '°', $voto['descrizione_ministeriale']);
//                    $chiaveDatiFlusso = $studente['classe'] . ' ' . $studente['sezione'] . ' ' . $studente['cognome'] . ' ' . $studente['nome'] . ' - ' . $studente['codice_fiscale'] . ' - ' . $voto['descrizione'];
                    $chiaveDatiFlusso = $studente['id_studente'] . '-' . $voto['id_materia'] . '-' . $voto['tipo_voto_usato'];

                    $periodoAttivo = $this->getPeriodoAttivo($periodo);
                    if (in_array($periodoAttivo['valore'], [7, 27]))
                    {
                        $esitoFineAnno = 0;
                    }

                    $valore_anno_scolastico = explode('/', $annoScolastico['valore'])[0] . substr(explode('/', $annoScolastico['valore'])[1], 2, 2);

                    if ($validitaAs == 2 && in_array($esitoFineAnno, [11, 27]))
                    {
                        $creditoScolastico = "";
                    }
                    
                    $datiFlusso[$chiaveDatiFlusso] = [
                            "codice_ministeriale"           => explode('-', $meccanografico)[1],
                            "anno_scolastico"               => $valore_anno_scolastico,
                            "frazione_temporale"            => $frazioneTemporale,
                            "codice_sidi"                   => $studente['codice_alunno_ministeriale'],
                            "anno_di_corso"                 => $studente['anno_cron'],
                            "indirizzo_ministeriale"        => trim($studente['codice_ministeriale']),
                            "validita_anno"                 => $validitaAs,
                            "esito_sidi"                    => $esitoFineAnno,
                            "codice_materia_ministeriale"   => trim($voto['codice_ministeriale']),
                            "codice_materia_locale"         => trim($voto['codice_ministeriale']),
                            "descrizione_materia"           => trim($descrizioneMinisteriale) . "@@" . $voto['tipo_voto_usato'],
                            "voto"                          => $descrizioneVoto,
                            "assenze"                       => $numeroAssenze,
                            "esito_integrazione"            => $esitoIntegrazione,
                            "credito_scolastico"            => $creditoScolastico,
                        ];                    
                }
            }
        }   

//    echo "finito {$meccanografico} - {$periodo} con un totale di " . count($datiFlusso) . "!";
//    
//    print "<pre>";
//    print_r($datiFlusso);
//    print "</pre>";
    
    
    $result[$meccanografico]['valutazioni'] = $datiFlusso;

    return $result;
    //}}} </editor-fold>
    }
    
    /**
     * Crea il file .txt contenente le valutazioni di fine anno
     * per ogni studente elaborato
     *
     * @param string $meccanografico
     * @return mixed
     */
    private function valutazioni($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $mecc = explode('-', $meccanografico);

//        $datiFlusso = [];

        $annoScolastico = $this->getAnnoScolastico();

        $datiStudente = $this->getStudenti($meccanografico);
        
        // Estraggo i valori delle valutazioni a parte per "riutilizzare" la funzionalità
        // anche per le estrazioni di SCUOLANET (ex RiminInRete).
        $result = $this->getValutazioni($datiStudente, $meccanografico);
        
        $datiValutazioni = $result[$meccanografico]['valutazioni'] ;
                
        foreach ($datiValutazioni as $id_valutazione => $valutazione)
        {
            $datiFlusso[$id_valutazione] = implode("|", $valutazione) . "|\n";
        }
        $datiFinali = array_values($datiFlusso);

        // Assenze
        $datiAssenze = $this->getAssenze($datiStudente);
        foreach ($datiAssenze as $id_assenza => $assenze)
        {
            $dati_finali_assenze[$id_assenza] = implode("|", $assenze) . "|\n";
        }
        $datiFinaliAssenze = $dati_finali_assenze;

        // Competenze
        $datiCompetenze = $this->getCompetenze($datiStudente);

        foreach ($datiCompetenze as $id_competenza => $competenza)
        {
            $dati_finali_competenze[$id_competenza] = implode("|", $competenza) . "|\n";
        }
        $datiFinaliCompetenze = $dati_finali_competenze;

        // ---------------

        $codici_scuola = $this->getCodiciScuola($meccanografico);
        $timestamp = date('YmdHi');

        $prefisso_file = $mecc[1] .
                        $codici_scuola['codice_prenotazione'] .
                        $this->formatId($codici_scuola['id_scuola']) .
                        explode("/", $annoScolastico['valore'])[0] .
                        $this->flusso_sidi .
                        "MAST" .
                        $this->versione .
                        $timestamp;

        // Genero i file necessari

        if (empty($result[$meccanografico]['errors_stop']))
        {
            $result[$meccanografico]['correct'] = 'File elaborato correttamente';

            // elimino i vecchi file
            exec("rm /var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/*" . $this->flusso_sidi . "MAST". $this->versione . "*");

            // genero il file scrutini
            $filename_scrutini = $prefisso_file . "001TB_ALU_MAT_VALUT.TXT";

            foreach ($datiFinali as $righe)
            {
                file_put_contents("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/" . $filename_scrutini, print_r($righe, true), FILE_APPEND);
            }

            // Creo il file Assenze (va creato anche se vuoto)
            $filename_assenze = $prefisso_file . "002TB_ALU_ASSENZE.TXT";

            file_put_contents("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/" . $filename_assenze, "", FILE_APPEND);
            if (!empty($datiFinaliAssenze))
            {
                foreach ($datiFinaliAssenze as $riga_assenza)
                {
                    file_put_contents("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/" . $filename_assenze, print_r($riga_assenza, true), FILE_APPEND);
                }
            }
            // Creo il file Competenze (va creato anche se vuoto)
            $filename_competenze = $prefisso_file . "003TB_ALU_CERT_COMP.TXT";

            file_put_contents("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/" . $filename_competenze, "", FILE_APPEND);

            if (!empty($datiFinaliCompetenze))
            {
                foreach ($datiFinaliCompetenze as $riga_competenza)
                {
                    file_put_contents("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/" . $filename_competenze, print_r($riga_competenza, true), FILE_APPEND);
                }
            }
            // Creo lo Zip
            $this->createZip($prefisso_file, explode('-', $meccanografico)[1], $timestamp);

            $fileZip = glob('/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/SCRUTINI_FINALI_' . explode('-',$meccanografico)[1] . '_' . $timestamp . '.zip');
            $zip = new ZipArchive;

            // Creo ed inserisco il file .dat
            file_put_contents("/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/##" . $prefisso_file . ".dat", "");

            foreach ($fileZip as $zipFile)
            {
                if ($zip->open($zipFile) === TRUE)
                {
                    $zip->addFile('/var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/##'. $prefisso_file. '.dat', '##' . $prefisso_file.'.dat')
                            or die ("ERROR: Could not add file: {$prefisso_file}");
                    $zip->close();
                }
            }

            exec("rm /var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/##*" . $this->flusso_sidi . "MAST". $this->versione . "*.dat");
        }

        file_put_contents('/tmp/result', print_r($result, true));

        return $result[$meccanografico]['errors_stop'];
    //}}} </editor-fold>
    }

    /**
     * Verifica preliminare dati
     *
     * @param string $meccanografico
     * @return mixed
     */
    public function verify($meccanografico)
    {
    //{{{ <editor-fold defaultstate="collapsed">
        $result[$meccanografico] = ['errors_stop' => '', 'warning' => ''];
        
        $anno_scolastico = $this->getAnnoScolastico();
        $ordine_scuola = $this->getOrdineScuola($meccanografico);
        $scuola = $this->getScuola($meccanografico);

        if (!empty($scuola['errors']))
        {
            $result[$meccanografico]['errors_stop'][] = [
                'desc'   => 'Codice di Prenotazione',
                'valore' => $scuola['errors']['codice_prenotazione']
            ];
            return $result[$meccanografico]['errors_stop'];
        }
        
        if ($ordine_scuola['ordine_scuola'] !== 'MM' && $ordine_scuola['ordine_scuola'] !== 'IC')
        {
            $qudriOrarioMancanti = $this->checkEsistenzaQo($anno_scolastico);

            if (!empty($qudriOrarioMancanti['errors']))
            {
                foreach ($qudriOrarioMancanti['errors']['mancano_quadri_orario'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Nessun Quadro Orario presente',
                        'valore' => $value
                    ];
                }
                return $result[$meccanografico]['errors_stop'];
            }
            else
            {
                if (!empty($abbinamentiErrati['errors']))
                {
                    foreach ($abbinamentiErrati['errors']['abbinamenti_mancanti'] as $value)
                    {
                        $result[$meccanografico]['errors_stop'][] = [
                            'desc'   => 'Abbinamenti Errati Quadri Orario',
                            'valore' => $value
                        ];
                    }
                    return $result[$meccanografico]['errors_stop'];
                }

                $qoDaSalvare = $this->getQuadriOrarioDaSalvare($meccanografico);

                if (!empty($qoDaSalvare) && is_array($qoDaSalvare))
                {
                    if (array_key_exists(0, $qoDaSalvare))
                        {
                        foreach ($qoDaSalvare as $value)
                        {
                            $result[$meccanografico]['errors_stop'][] = [
                                'desc'   => 'Quadri Orario da Salvare in Setup A15',
                                'valore' => $value['descrizione']
                            ];
                        }
                    }
                    else
                    {
                        $result[$meccanografico]['errors_stop'][] = [
                            'desc'   => 'Quadri Orario da Salvare in Setup A15',
                            'valore' => $qoDaSalvare['descrizione']
                        ];
                    }
                    return $result[$meccanografico]['errors_stop'];
                }

                $studentiNoQo = $this->getStudentiSenzaQo($meccanografico);

                if (!empty($studentiNoQo) && is_array($studentiNoQo))
                {
                    if (array_key_exists(0, $studentiNoQo))
                    {
                        foreach ($studentiNoQo as $value)
                        {
                            $result[$meccanografico]['errors_stop'][] = [
                                'desc'   => 'Studente senza Quadro Orario in Setup A14',
                                'valore' => $value['cognome']. ' '. $value['nome'] .' - '.$value['classe'].$value['sezione']. ' '.$value['descrizione_indirizzi']
                            ];
                        }
                    }
                    else
                    {
                        $result[$meccanografico]['errors_stop'][] = [
                            'desc'   => 'Studente senza Quadro Orario in Setup A14',
                            'valore' => $studentiNoQo['cognome']. ' '. $studentiNoQo['nome'] .' - '.$studentiNoQo['classe'].$studentiNoQo['sezione']. ' '.$studentiNoQo['descrizione_indirizzi']
                        ];
                    }
                    return $result[$meccanografico]['errors_stop'];
                }
            }

            if (!empty($utilizzoMateriePagella['errors']))
            {
                if (array_key_exists(0, $utilizzoMateriePagella['errors']['utilizzo_materie_in_pagella']))
                {
                    foreach ($utilizzoMateriePagella['errors']['utilizzo_materie_in_pagella'] as $value)
                    {
                        $result[$meccanografico]['errors_stop'][] = [
                            'desc'   => 'Utilizzo Materie in Pagella',
                            'valore' => $value
                        ];
                    }
                }
                else
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Utilizzo Materie in Pagella',
                        'valore' => $utilizzoMateriePagella['errors']['utilizzo_materie_in_pagella'][0]
                    ];
                }
                return $result[$meccanografico]['errors_stop'];
            }
        }        
        
        if (empty($result[$meccanografico]['errors_stop']))
        {
        
            // Recupero i dati di tutti gli studenti
            $datiStudente = $this->getStudenti($meccanografico, null, 'check');

            // Controlli dati studente
            if (!empty($datiStudente['errors']))
            {
                if (!empty($datiStudente['errors']['nessun_dato_presente']))
                {
                    foreach ($datiStudente['errors']['nessun_dato_presente'] as $value)
                    {
                        $result[$meccanografico]['errors_stop'][] = [
                            'desc'   => 'Nessun dato presente',
                            'valore' => $value
                        ];
                    }

                    return $result[$meccanografico]['errors_stop'];
                }

                if (!empty($datiStudente['errors']['codice_sidi']))
                {
                    foreach ($datiStudente['errors']['codice_sidi'] as $value)
                    {
                        $result[$meccanografico]['errors_stop'][] = [
                            'desc'   => 'Codice Sidi',
                            'valore' => $value
                        ];
                    }
                }
            }

            if (empty($datiStudente['errors']))
            {
                // Recupero i dati delle assenze degli studenti selezionati
                $datiAssenze = $this->getAssenze($datiStudente, 'check');
            }

            // Controlli dati assenze
            if (!empty($datiAssenze['errors']))
            {
                foreach ($datiAssenze['errors']['codice_sidi'] as $value)
                {
                    $result[$meccanografico]['errors_stop'][] = [
                        'desc'   => 'Assenze',
                        'valore' => $value
                    ];
                }
            }
        }

        if (!empty($result[$meccanografico]['errors_stop']))
        {
            return $result[$meccanografico]['errors_stop'];
        }
        else
        {
            return true;
        }
    //}}} </editor-fold>
    }

    public function generate($meccanografico)
    {
        return $this->valutazioni($meccanografico);
    }
}