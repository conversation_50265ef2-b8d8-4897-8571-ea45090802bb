;-------------------------------------------------------------------------------
; Package configuration
;-------------------------------------------------------------------------------

[application]
;
; Application name, will be master-<name>
;
;name = myApp
;
name = flussi

;
; Application version
;
;version = 1.0.0
;
version = 1.1.57

;
; Debian package configuration for Control file
;
; NOTE: "breaks" IS NOT SUPPORTED by dpkg of Debian 4.0 release.
;
;replaces =
;breaks =
;conflicts =
;section = main
;priority = optional
;architecture = all
;depends[] = ""
;maintainer = "<PERSON> <<EMAIL>>"
;description = ""
;
section = main
priority = optional
architecture = all
depends[] = "php5 (>=5.5.35) | php (>=1:7.0)"
depends[] = "php5-pgsql (>=5.5.35) | php-pgsql (>=1:7.0)"
depends[] = "apache2 (>=2.4.7)"
depends[] = "libapache2-mod-php5 (>=5.5.35) | libapache2-mod-php (>=1:7.0)"
depends[] = "master-apache (>=3.2.4)"
depends[] = "master-logger (>=1.1.30)"
depends[] = "master-mt (>=1.1.39)"
maintainer = "Francesco Torregrossa <<EMAIL>>"
description = "Flussi (Siis, Regione Valle d'Aosta, Trentino Alto Adige)"

;
; Source path of the application in development environment
;
;src_path = /home/<USER>
;
src_path = /var/lib/mercurial-server/repos/flussi

;
; Destination path of the application
;
;dest_path = /var/www/myApp
;
dest_path = /var/www-source/flussi

;
; Configuration files (Debian conffiles)
;
; If your application uses configuration files but also rewrites them on its
; own, it's best not to make them conffiles because dpkg will then prompt users
; to verify the changes all the time.
;
; conffiles[] = /var/www/myApp/configuration.ext
; conffiles[] = /var/www/myApp/configuration_2.ext
;
conffiles[] =

;
; If specified the package manager will backups all files/directories in
; /var/backups/mastertraining/<APPLICATION NAME> before install or upgrade the
; package.
; NOTE: you must set the absolute path of the files/directories. The directory
;       tree will be replicated into the backup directory.
;       Symbolic links are not supported yet.
;
;backup[] = /etc/myconf
;

;
; Lock script executed before updating application.
; The script must to be into RELEASE path, and the extension must be .sh or .php
;
; IMPORTANT: also unlock_script must to be specified
;
;lock_script = lock.sh
;

;
; Unlock script executed after updating application
; The script must to be into RELEASE path, and the extension must be .sh or .php
;
; IMPORTANT: also lock_script must to be specified
;
;unlock_script = unlock.sh
;

;
; Define the list of directories/files to ignore for inclusion.
; NOTE: You must define the absolute path
;
;exclude[] = /var/www/myApp/PathToIgnore
;exclude[] = /var/www/myApp/FileToIgnore.ext
;
;exclude[] = /var/lib/mercurial-server/repos/flussi/data/cache

;
; Check PHP syntax errors
;
;check_php_errors = on
;
check_php_errors = off ; usare solo quando ci sarà PHP 5.4 con supporto "trait"
                       ; altrimenti va in errore su:
                       ; Zend/Db/Adapter/AdapterAwareTrait.php

;-------------------------------------------------------------------------------
; Database parameters
;-------------------------------------------------------------------------------
[database]
;
; Specify the configuration file (using the absolute path) where database
; connection parameters are defined for the application.
;
; IMPORTANT:
; - See also label_<ALL> parameters
; - The content format must be like:
;
;   define('MY_DB_HOST_CONSTANT', 'myhost');
;   define('MY_DB_PORT_CONSTANT', 5432);
;   ...
;
;configuration = /var/www-source/gathering/config/autoload/global.php
;
configuration =

;
; If off force to skip database creation. To use for eg. with a new schema in an
; existing database
;
create_database = off

;
; The labels regexp where package builder must find relative configuration
; parameters.
;
label_host =
label_port =
label_name =
label_user =
label_password =
;
;label_host =
;label_port =
;label_name =
;label_user =
;label_password =

;
; Default database configuration
;
; NOTE: Port number will be updated if update_port is "on"
;
;name = myAppDatabaseName
;port = 5432
;host = localhost
;user = ********
;password = ********
;
name =
schema =
port =
host =
user =
password =

;
; If "on" the package manager will check the database port if differs from
; current running port, if so the configuration file will be updated.
;
;update_port = on
;
update_port = off

;
; Make a database backup before updating application.
; The backup will be saved as gzip file into:
; /var/lib/********ql/mastertraining/<APPLICATION NAME>/backup.sql.gz
;
;backup = on
;
backup = off

;
; Database optimization after install
;
;reindex = on
;vacuum = on
;analyze = on
;
reindex = off
vacuum = off
analyze = off

;
; Table parameters
;
;parameters_table = parameters
;parameter_name_column = name
;parameter_value_column = value
;version_label = VERSION
;
parameters_table =
parameter_name_column =
parameter_value_column =
version_label =

;
; Restart / Reload services after install
;
[services]
apache_reload = off
apache_restart = off
********ql_reload = off
********ql_restart = off

;
; Code Optimizer
;
[optimizer]

;
; Minify/Precompile application code for the final package.
; Files in <APPLICATION NAME>/configs/ path will be ignored
;
;php = on
;js = on
;css = on
;html = on
php = off ; NON ABILITARE (i commenti servono per i WSDL)
js = on
css = on
html = on

;
; Define the list of the files to ignore for optimization.
; NOTE: Exclude directories are not implemented yet.
;
;exclude[] = /var/www/myApp/fileToIgnore.php
;

;-------------------------------------------------------------------------------
; On each update executes specified files into the defined order
;-------------------------------------------------------------------------------
[execute]
;
;file[] = update.sql
;
file[] = update.sql
