<?php

require_once '../../index.php';

$nome_flusso = filter_input(INPUT_GET, 'flusso');
$mecc_scuola = filter_input(INPUT_GET, 'meccanografico');
$operazione = filter_input(INPUT_GET, 'operazione');
$as = filter_input(INPUT_GET, 'as');

$flusso = new Siis($nome_flusso, $as);
$flow = $flusso->$nome_flusso($mecc_scuola, $operazione, $as);

file_put_contents('/tmp/flow', json_encode((array) $flow), FILE_APPEND);

exit(json_encode((array) $flow));
